output "sg_id" {
  value = module.cluster.security_group_id
}

output "cluster_arn" {
  description = "The cluster arn"
  value       = module.cluster.cluster_arn
}

output "cluster_id" {
  description = "The cluster id"
  value       = module.cluster.cluster_id
}

output "cluster_reader_endpoint" {
  description = "The cluster reader endpoint"
  value       = module.cluster.cluster_reader_endpoint
}

output "additional_cluster_endpoints" {
  description = "The cluster instance endpoints"
  value       = module.cluster.additional_cluster_endpoints
}

output "cluster_endpoint" {
  description = "The cluster endpoint"
  value       = module.cluster.cluster_endpoint
}
