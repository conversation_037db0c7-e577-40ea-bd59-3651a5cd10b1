import json
import os
from pathlib import Path

import environ
from kombu import Exchange
from kombu import Queue
from pythonjsonlogger.json import JsonFormatter


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

env = environ.Env()
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))
SECRET_KEY = env("SECRET_KEY")

DEBUG = env("DEBUG", default="False").lower() == "true"

ALLOWED_HOSTS = json.loads(env("ALLOWED_HOSTS", default="[]"))

CORS_ORIGIN_ALLOW_ALL = True
CSRF_TRUSTED_ORIGINS = json.loads(env("CSRF_TRUSTED_ORIGINS", default="[]"))

STATICFILES_STORAGE = "storages.backends.s3.S3Storage"
AWS_STORAGE_BUCKET_NAME = env("AWS_STORAGE_BUCKET_NAME", default="sa-faas-statics")
AWS_S3_CUSTOM_DOMAIN = env(
    "AWS_S3_CUSTOM_DOMAIN", default="faas-statics.devsuperannotate.com"
)

AWS_LOGS_BUCKET_NAME = env("AWS_LOGS_BUCKET_NAME", default="sa-faas-dev")
AWS_LOGS_PATH = env("AWS_LOGS_PATH", default="faas-jobs")
AWS_REPORTS_PATH = env("AWS_REPORTS_PATH", default="reports")

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "corsheaders",
    "django_celery_beat",
    "django_celery_results",
    "drf_yasg",
    "apps.executor",
    "apps.user",
    "admin_list_charts",
    "django_admin_inline_paginator",
    "storages",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "stockhausen.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "%(asctime)s;%(levelname)s;%(message)s;",
            "datefmt": "%y %b %d, %H:%M:%S",
        },
        "json": {
            "()": JsonFormatter,
            "format": "%(asctime)s %(levelname)s %(name)s %(message)s",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
    },
    "loggers": {
        "celery": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "celery.beat": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django": {
            "handlers": ["console"],
            "level": env("DJANGO_LOG_LEVEL", default="INFO"),
            "propagate": False,
        },
        "django.request": {
            "handlers": ["console"],
            "level": env("DJANGO_LOG_LEVEL", default="INFO"),
            "propagate": False,
        },
        "gunicorn": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
    },
}

WSGI_APPLICATION = "stockhausen.wsgi.application"


if not env("DB_NAME", default=None):
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        },
        "replica1": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        },
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql_psycopg2",
            "NAME": env("DB_NAME"),
            "USER": env("DB_USER"),
            "PASSWORD": env("DB_PASSWORD"),
            "HOST": env("WRITE_DB_HOST"),
            "PORT": "",
            "CONN_MAX_AGE": 120,
        }
    }
    if env("READ_DB_HOST", default=None):
        DATABASES["replica1"] = {
            **DATABASES["default"],
            "HOST": env("READ_DB_HOST"),
        }
        DATABASE_ROUTERS = ["apps.executor.db_router.PrimaryReplicaRouter"]

AUTH_USER_MODEL = "user.User"
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 100,
}
CACHE_IGNORED_JOBS = "ignored_jobs"

KUBERNETES_NAMESPACE = env("KUBERNETES_NAMESPACE", default="default")
KUBERNETES_SERVICE_ACCOUNT_NAME = env(
    "KUBERNETES_SERVICE_ACCOUNT_NAME", default="sa-faas-jobs-sa"
)
SERVER_HOST = env("SERVER_HOST")
RECEIVED_STATE_TIMEOUT = int(env("JOB_WAITING_TIMEOUT", default="10800"))  # 3 hour
STARTED_STATE_TIMEOUT = int(env("STARTED_STATE_TIMEOUT", default="10800"))  # 3 hour
POD_WAITING_TIMEOUT = int(env("POD_WAITING_TIMEOUT", default="3600"))  # 1 hour

HEALTH_CHECK = {
    "DISK_USAGE_MAX": int(env("HEALTH_CHECK_DISK_USAGE_MAX", default="90")),
    "MEMORY_MIN": int(env("HEALTH_CHECK_MEMORY_MIN", default="100")),
    "CELERY_PING_TIMEOUT": int(env("HEALTH_CHECK_CELERY_PING_TIMEOUT", default="1")),
}

WORKERS_QUEUE = env("WORKERS_QUEUE", default="workers")
WORKERS_CLEANUP_QUEUE = env("WORKERS_CLEANUP_QUEUE", default="workers_cleanup")
AUDIT_QUEUE = env("AUDIT_QUEUE", default="audit")

REDIS_URL = env("REDIS_URL")
CACHE_RUNNING_KEY = "running"
CACHE_REVOKE_CONDITIONS_KEY = "revoke_conditions"
CACHE_REVOKE_CONDITIONS_TTL = env("CACHE_REVOKE_CONDITIONS_TTL", default=4)  # hour

REVOKED_BLUEPRINT_TTL = 3  # hour

CELERY_RESULT_BACKEND = "celery.backends.s3.S3Backend"
CELERY_S3_BUCKET = AWS_LOGS_BUCKET_NAME
CELERY_S3_BASE_PATH = "celery/"
CELERY_RESULT_EXTENDED = True
CELERY_BROKER_URL = env("CELERY_BROKER_URL")
CELERY_ACKS_LATE = True
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_WORKER_PREFETCH_MULTIPLIER = 8

workers_exchange = Exchange("s_workers", type="topic", durable=True)
workers_cleanup_exchange = Exchange("s_workers_cleanup", type="topic", durable=True)
audit_exchange = Exchange("s_audit", type="topic", durable=True)

CELERY_TASK_QUEUES = (
    # Explicitly declare the exchange first
    Queue(
        WORKERS_QUEUE,
        exchange=workers_exchange,
        routing_key=WORKERS_QUEUE,
        queue_arguments={"x-queue-type": "quorum"},
    ),
    Queue(
        WORKERS_CLEANUP_QUEUE,
        exchange=workers_cleanup_exchange,
        routing_key=WORKERS_CLEANUP_QUEUE,
        queue_arguments={"x-queue-type": "quorum"},
    ),
    Queue(
        AUDIT_QUEUE,
        routing_key=AUDIT_QUEUE,
        exchange=audit_exchange,
        queue_arguments={"x-queue-type": "quorum"},
    ),
)

CELERY_TASK_SERIALIZER = "json"
CELERY_TASK_DEFAULT_EXCHANGE = "executor"
CELERY_TASK_DEFAULT_EXCHANGE_TYPE = "topic"
CELERY_TASK_DEFAULT_ROUTING_KEY = "executor"

SERVICE_NAME = "faas"
REDIS_SECRETS = "secrets"
SA_EVENT_URL = env(
    "SA_EVENT_URL",
    default="http://sa-kondo-api.automation.svc.cluster.local:8080/api/v1/automation/publish",
)

KAFKA_METRICS_TOPIC = env("KAFKA_METRICS_TOPIC", default="faas_jobs_metrics")
KAFKA_CONSUMER_GROUP = env("KAFKA_CONSUMER_GROUP", default="stockhausen")
KAFKA_BOOTSTRAP_SERVERS = env("KAFKA_BOOTSTRAP_SERVERS")
