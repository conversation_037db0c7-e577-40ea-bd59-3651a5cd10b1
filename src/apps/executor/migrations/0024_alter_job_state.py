# Generated by Django 4.2 on 2024-07-23 05:45
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("executor", "0023_job_headers"),
    ]

    operations = [
        migrations.AlterField(
            model_name="job",
            name="state",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("RECEIVED", "Received"),
                    ("STARTED", "Started"),
                    ("SUCCESS", "Success"),
                    ("FAILURE", "Failure"),
                    ("REVOKED", "Revoked"),
                    ("RETRY", "Retry"),
                    ("IGNORED", "Ignored"),
                    ("TIMEOUT", "Timeout"),
                    ("UNK<PERSON>OWN", "Unknown"),
                ],
                db_index=True,
                default="RECEIVED",
                max_length=128,
            ),
        ),
    ]
