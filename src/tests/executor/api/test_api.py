import json
from os.path import join
from unittest.mock import patch

from django.test.utils import override_settings
from rest_framework import status as http_status
from rest_framework.test import APITestCase
from tests import DATA_SET_PATH


@override_settings(DATABASE_ROUTERS=[])
class APITest(APITestCase):
    databases = {"default"}
    multi_db = False
    API_PREFIX = "/api/v1"
    API_BLUEPRINT = f"{API_PREFIX}/job_blueprint/"
    API_JOB = f"{API_PREFIX}/job"
    API_RUN_JOB = f"{API_PREFIX}/job/run/"
    API_POST_POSTFIX = "patch/"
    API_REVOKE_POSTFIX = "revoke/"
    API_REVOKE_RESULT = f"{API_PREFIX}/revoke_result/"

    PATH_BLUEPRINT_CREATE_PAYLOAD = join(
        DATA_SET_PATH, "api", "blueprint_create_payload.json"
    )
    PATH_JOB_CREATE_PAYLOAD = join(
        DATA_SET_PATH, "api", "job_result_create_payload.json"
    )
    PATH_POST_RESULT_PAYLOAD = join(DATA_SET_PATH, "api", "post_result_payload.json")
    JOB_FIELDS_TO_EXCLUDE = (
        "team_id",
        "org_id",
        "callback_url",
        "headers",
        "vault_path",
        "vault_version",
    )

    def _create_job(self, payload: dict):
        return self.client.post(
            f"{self.API_PREFIX}/job/",
            payload,
            format="json",
        )

    @patch("apps.executor.serializers.run_job")
    def test_flow(self, f_run_job):
        """
        1. create job
        2. run job
            - check 'apps.executor.tasks.run_job' triggered with correct arguments
        3. post result
        4. get result
        5. revoke
        """
        blueprint_payload = json.load(open(self.PATH_BLUEPRINT_CREATE_PAYLOAD))
        job_response = self.client.post(
            self.API_BLUEPRINT, blueprint_payload, format="json"
        )
        response_data = job_response.json()
        blueprint_id = response_data.pop("id")
        response_data.pop("selector")
        self.assertDictEqual(response_data, blueprint_payload)
        assert job_response.status_code == http_status.HTTP_201_CREATED

        job_res_create_payload = json.load(open(self.PATH_JOB_CREATE_PAYLOAD))
        job_res_create_payload["blueprint"] = blueprint_id
        job_create_response = self.client.post(
            f"{self.API_RUN_JOB}", job_res_create_payload, format="json"
        )
        assert job_create_response.status_code == http_status.HTTP_201_CREATED
        job_result_create_data = job_create_response.data
        result_id = job_result_create_data.pop("id")
        for key in self.JOB_FIELDS_TO_EXCLUDE:
            del job_result_create_data[key]
        self.assertDictEqual(job_result_create_data, job_res_create_payload)
        run_job_headers = f_run_job.apply_async.call_args_list[0].kwargs["headers"]
        run_job_kwargs = f_run_job.apply_async.call_args_list[0].kwargs["kwargs"]
        assert run_job_headers["blueprint_id"] == blueprint_id
        assert run_job_kwargs["blueprint_id"] == blueprint_id
        assert run_job_kwargs["job_id"] == result_id
        assert run_job_kwargs["args_type"] == job_res_create_payload["args_type"]

        job_res_payload = json.load(open(self.PATH_POST_RESULT_PAYLOAD))
        job_res_payload["job"] = blueprint_id
        base = join(self.API_JOB, str(result_id) + "/")

        job_result_response = self.client.patch(
            join(base, self.API_POST_POSTFIX), job_res_payload, format="json"
        )
        assert job_result_response.status_code == http_status.HTTP_200_OK
        assert (
            job_result_response.json()["message"]
            == f"Update job {result_id} request submitted."
        )

    @patch("apps.executor.serializers.run_job")
    def test_flow_by_selector(self, f_run_job):
        """
        1. create job
        2. run job
            - check 'apps.executor.tasks.run_job' triggered with correct arguments
        3. post result
        4. get result
        5. revoke
        """
        job_payload = json.load(open(self.PATH_BLUEPRINT_CREATE_PAYLOAD))
        job_payload["selector"] = "selector"
        job_response = self.client.post(self.API_BLUEPRINT, job_payload, format="json")
        job_data_dict = job_response.data
        blueprint_id = job_data_dict.pop("id")
        self.assertDictEqual(job_data_dict, job_payload)
        assert job_response.status_code == http_status.HTTP_201_CREATED

        job_res_create_payload = json.load(open(self.PATH_JOB_CREATE_PAYLOAD))
        del job_res_create_payload["blueprint"]
        job_res_create_payload["selector"] = "selector"
        job_create_response = self.client.post(
            f"{self.API_RUN_JOB}", job_res_create_payload, format="json"
        )
        assert job_create_response.status_code == http_status.HTTP_201_CREATED
        job_result_create_data = job_create_response.data
        result_id = job_result_create_data.pop("id")
        for key in self.JOB_FIELDS_TO_EXCLUDE:
            del job_result_create_data[key]
        del job_res_create_payload["selector"]
        del job_result_create_data["blueprint"]
        self.assertDictEqual(job_result_create_data, job_res_create_payload)
        run_job_headers = f_run_job.apply_async.call_args_list[0].kwargs["headers"]
        run_job_kwargs = f_run_job.apply_async.call_args_list[0].kwargs["kwargs"]
        assert run_job_headers["blueprint_id"] == blueprint_id
        assert run_job_kwargs["blueprint_id"] == blueprint_id
        assert run_job_kwargs["job_id"] == result_id
        assert run_job_kwargs["args_type"] == job_res_create_payload["args_type"]

        job_res_payload = json.load(open(self.PATH_POST_RESULT_PAYLOAD))
        job_res_payload["job"] = blueprint_id
        base = join(self.API_JOB, str(result_id) + "/")

        job_result_response = self.client.patch(
            join(base, self.API_POST_POSTFIX), job_res_payload, format="json"
        )
        assert job_result_response.status_code == http_status.HTTP_200_OK
        assert (
            job_result_response.json()["message"]
            == f"Update job {result_id} request submitted."
        )

    def test_run_invalid_input_args(self):
        job_res_payload = {
            "input_args": {"k": 1},
            "args_type": "env",
            "selector": "dummy",
        }
        response = self.client.post(self.API_RUN_JOB, job_res_payload, format="json")
        assert (
            str(response.data["input_args"][0])
            == "Invalid value type for key 'k': 1 (must be a string)."
        )
