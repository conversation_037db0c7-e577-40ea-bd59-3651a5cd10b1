import json
from datetime import timed<PERSON>ta
from os.path import join
from unittest.mock import patch

from apps.executor.models import Job
from apps.executor.models import JobStateEnum
from apps.executor.tasks import run_job
from apps.executor.utils import get_redis_client
from django.conf import settings
from django.test.utils import override_settings
from django.utils import timezone
from rest_framework import status as http_status
from rest_framework.test import APITestCase
from tests import DATA_SET_PATH


r = get_redis_client()


@override_settings(DATABASE_ROUTERS=[])
class TestRevokeByCondition(APITestCase):
    databases = {"default"}

    API_PREFIX = "/api/v1"
    API_REVOKE_BY_CONDITION = f"{API_PREFIX}/job/revoke_by_condition/"
    API_BLUEPRINT = f"{API_PREFIX}/job_blueprint/"
    API_RUN_JOB = f"{API_PREFIX}/job/run/"
    PATH_BLUEPRINT_CREATE_PAYLOAD = join(
        DATA_SET_PATH, "api", "blueprint_create_payload.json"
    )
    PATH_JOB_CREATE_PAYLOAD = join(
        DATA_SET_PATH, "api", "job_result_create_payload.json"
    )
    REVOKE_CONDITION_BY_WORKFLOW_ID_AND_TIMESTAMP = {
        "condition": [
            {
                "source": "header",
                "key": "x_workflow_id",
                "op": "==",
                "value": 1234,
            },
            {
                "source": "header",
                "key": "x_received_timestamp",
                "op": "<=",
                "value": timezone.now().timestamp(),
            },
        ],
        "revoked_by": "test_user",
    }
    REVOKE_CONDITION_BY_KWARGS = {
        "condition": [
            {
                "source": "kwargs",
                "key": "input_args.EVENT.pipeline_id",
                "op": "==",
                "value": "1785",
            }
        ],
        "revoked_by": "test_user",
    }

    def setUp(self):
        self.tearDown()
        blueprint_payload = json.load(open(self.PATH_BLUEPRINT_CREATE_PAYLOAD))
        blueprint_res = self.client.post(
            self.API_BLUEPRINT, blueprint_payload, format="json"
        )
        assert blueprint_res.status_code == http_status.HTTP_201_CREATED
        self.blueprint_id = blueprint_res.json()["id"]

    def tearDown(self):
        r.zremrangebyscore(
            settings.CACHE_REVOKE_CONDITIONS_KEY, min=0, max=timezone.now().timestamp()
        )

    def _run_task(self):
        run_job.apply(
            kwargs={
                "blueprint_id": self.blueprint_id,
                "job_id": 1,
                "created_at": timezone.now().timestamp(),
                "args_type": "env",
                "input_args": {"EVENT": {"pipeline_id": "1785"}},
                "image": "",
            },
            headers={
                "blueprint_id": self.blueprint_id,
                "x_workflow_id": 1234,
                "x_received_timestamp": (
                    timezone.now() - timedelta(minutes=2)
                ).timestamp(),
            },
        )

    @patch("apps.executor.serializers.run_job")
    def _create_job(self, mock_run_job):
        job_res_create_payload = json.load(open(self.PATH_JOB_CREATE_PAYLOAD))
        job_res_create_payload["blueprint"] = self.blueprint_id
        job_create_response = self.client.post(
            f"{self.API_RUN_JOB}", job_res_create_payload, format="json"
        )
        assert job_create_response.status_code == http_status.HTTP_201_CREATED
        assert mock_run_job.apply_async.call_count == 1
        Job.objects.filter(pk=job_create_response.json()["id"]).update(
            uuid="job-1234", state="STARTED"
        )
        return job_create_response.json()

    @patch("apps.executor.task_handlers.TaskRevokedException")
    def test_bulk_revoke_by_workflow_id_and_timestamp(self, revoke_error):
        revoke_res = self.client.post(
            self.API_REVOKE_BY_CONDITION,
            self.REVOKE_CONDITION_BY_WORKFLOW_ID_AND_TIMESTAMP,
            format="json",
        )
        assert revoke_res.status_code == http_status.HTTP_200_OK

        # check redis revoke conditions
        assert (
            json.loads(r.zrange(settings.CACHE_REVOKE_CONDITIONS_KEY, 0, -1)[0])
            == self.REVOKE_CONDITION_BY_WORKFLOW_ID_AND_TIMESTAMP
        )

        self._run_task()
        assert revoke_error.call_count == 1

    @patch("apps.executor.task_handlers.TaskRevokedException")
    def test_bulk_revoke_by_kwargs(self, revoke_error):
        revoke_res = self.client.post(
            self.API_REVOKE_BY_CONDITION,
            self.REVOKE_CONDITION_BY_KWARGS,
            format="json",
        )
        assert revoke_res.status_code == http_status.HTTP_200_OK

        # check redis revoke conditions
        assert (
            json.loads(r.zrange(settings.CACHE_REVOKE_CONDITIONS_KEY, 0, -1)[0])
            == self.REVOKE_CONDITION_BY_KWARGS
        )

        self._run_task()
        assert revoke_error.call_count == 1

    @patch("apps.executor.tasks.terminate_k8s_job")
    @patch("apps.executor.task_handlers.TaskRevokedException")
    def test_revoke_by_blueprint(self, revoke_error, mock_terminate_k8s_job):
        job_id = self._create_job()["id"]
        revoke_res = self.client.post(
            f"{self.API_PREFIX}/job_blueprint/{self.blueprint_id}/revoke/"
        )
        assert revoke_res.status_code == http_status.HTTP_200_OK
        assert revoke_res.json()["message"] == "Blueprint revoked."
        assert mock_terminate_k8s_job.delay.call_count == 1
        assert Job.objects.get(id=job_id).state == JobStateEnum.REVOKED
        self._run_task()
        assert revoke_error.call_count == 1

    @patch("apps.executor.task_handlers.TaskRevokedException")
    def test_revoke_with_blueprint_disable(self, revoke_error):
        patch_res = self.client.patch(
            f"{self.API_PREFIX}/job_blueprint/{self.blueprint_id}/",
            data={"is_active": False},
        )
        assert patch_res.status_code == http_status.HTTP_200_OK
        self._run_task()
        assert revoke_error.call_count == 1

    @patch("apps.executor.task_handlers.TaskRevokedException")
    def test_revoke_with_blueprint_delete(self, revoke_error):
        patch_res = self.client.delete(
            f"{self.API_PREFIX}/job_blueprint/{self.blueprint_id}/"
        )
        assert patch_res.status_code == http_status.HTTP_204_NO_CONTENT
        self._run_task()
        assert revoke_error.call_count == 1

    @patch("apps.executor.tasks.terminate_k8s_job")
    def test_revoke_job(self, mock_terminate_k8s_job):
        job_data = self._create_job()
        revoke_res = self.client.post(f"{self.API_PREFIX}/job/{job_data['id']}/revoke/")
        assert revoke_res.status_code == http_status.HTTP_200_OK
        assert mock_terminate_k8s_job.delay.call_count == 1
