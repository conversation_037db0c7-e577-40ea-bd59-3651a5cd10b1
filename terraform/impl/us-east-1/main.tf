module "aurora_postgresql" {
  source = "../../module"

  db_ingress_sg_subnets             = [ "10.77.0.0/16", "178.160.196.42/32" ]
  name                              = "sa-faas-db"
  db_name                           = "stockhausen"
  engine                            = "aurora-postgresql"

  db_parameter_group_family         = "aurora-postgresql13"
  db_cluster_parameter_group_family = "aurora-postgresql13"
  engine_version                    = "13.8"
  region                            = "us-east-1"
  username                          = "faas"
  # password is generated and stored in AWS Secrets Manager
  # you can get the password by navigating to the RDS Console
  # open the secret then retrieve the password value from there
  writer_instance_class             = "db.t3.medium"
  reader_instance_class             = "db.t3.medium"

  # Tags related
  env                = "PROD"
  db_tag_description = "SA-FAAS aka StockHausen"

  # tfstate data source
  vpc_tfstate_bucket = "sa-terraform-project"
  vpc_tfstate_key    = "prod/north-virginia/prod-main-vpc/terraform.tfstate"
  vpc_tfstate_region = "us-west-2"
}
