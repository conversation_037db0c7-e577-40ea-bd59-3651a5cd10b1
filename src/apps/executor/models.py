import os
import pickle

from apps.executor.utils import get_redis_client
from apps.executor.utils import VaultManager
from django.core.validators import MaxValueValidator
from django.core.validators import MinValueValidator
from django.db import models
from django.db import transaction
from django.forms.models import model_to_dict
from django.utils import timezone
from django.utils.html import format_html


class BaseCacedModelMxin:
    @classmethod
    def get_cached(cls, pk):
        r = get_redis_client()
        key = cls.__name__
        pickled_instance = r.hget(key, pk)
        if not pickled_instance:
            instance = cls.objects.get(pk=pk)  # noqa
            r.hset(key, repr(instance), pickle.dumps(instance))
            return instance
        return pickle.loads(pickled_instance)

    @classmethod
    def reset(cls):
        r = get_redis_client()
        key = cls.__name__
        instance = cls.objects.get(pk=pk)  # noqa
        r.hset(key, repr(instance), pickle.dumps(instance))


class ImageTypeEnum(models.IntegerChoices):
    INTERNAL = 1
    DOCKER_HUB = 16
    ECR = 32
    GCR = 64


class ArgumentsTypeEnum(models.TextChoices):
    CLI = "cli"
    ENV = "env"


class OutputTypeEnum(models.TextChoices):
    JSON = "application/json"
    BINARY = "application/unknown"


class VaultSecret(models.Model):
    path = models.CharField(max_length=256, unique=True)
    version = models.IntegerField()

    def __repr__(self):
        return self.path

    def __str__(self):
        return f"{self.path}:{self.version}"


class JobBlueprint(models.Model):
    type = models.PositiveIntegerField(choices=ImageTypeEnum.choices)
    name = models.CharField(max_length=256, null=True, blank=True)
    image = models.CharField(max_length=256)
    ttl = models.IntegerField(default=10800)  # seconds
    max_jobs = models.IntegerField(default=32)
    priority = models.IntegerField(
        default=5, validators=[MinValueValidator(1), MaxValueValidator(10)]
    )
    is_active = models.BooleanField(default=True)
    node_toleration = models.CharField(
        max_length=128, null=True, blank=True, default="FAAS"
    )
    primary = models.BooleanField(default=False)
    selector = models.CharField(max_length=256, null=True, blank=True, unique=True)
    service_account = models.CharField(null=True, blank=True, max_length=256)
    use_persistent_volume = models.BooleanField(default=False)
    vault = models.ForeignKey(
        VaultSecret, null=True, blank=True, on_delete=models.SET_NULL
    )
    cpu = models.CharField(max_length=128, null=True, blank=True)
    memory = models.CharField(max_length=128, null=True, blank=True)

    def __repr__(self):
        return str(self.id)

    def __str__(self):
        return f"{self.name} {f'| {self.selector}' if self.selector else ''}"

    def save(self, *args, **kwargs):
        if self.id:
            previous_obj = JobBlueprint.objects.get(id=self.id)
            if previous_obj.is_active != self.is_active and not self.is_active:
                self.revoke()
        with transaction.atomic():
            super().save(*args, **kwargs)
            self._sync_with_vault()

    def delete(self, *args, **kwargs):
        self.revoke()
        with transaction.atomic():
            super().delete(*args, **kwargs)
            self._sync_with_vault(delete=True)

    def revoke(self):
        from apps.executor.tasks import terminate_k8s_job
        from apps.executor.utils import set_redis_revoke_condition

        running_jobs = self.job_set.filter(
            state__in=JobStateEnum.get_running_states(), uuid__isnull=False
        )
        for job in running_jobs:
            terminate_k8s_job.delay(job.uuid, self.id)
        running_jobs.update(state=JobStateEnum.REVOKED)

        revoke_condition = {
            "condition": [
                {
                    "source": "header",
                    "key": "blueprint_id",
                    "op": "==",
                    "value": self.id,
                },
                {
                    "source": "header",
                    "key": "x_received_timestamp",
                    "op": "<=",
                    "value": timezone.now().timestamp(),
                },
            ],
            "revoked_by": "Blueprint",
        }
        set_redis_revoke_condition(revoke_condition)

    def _sync_with_vault(self, delete: bool = False):
        """
        Sync primary blueprint data with Vault by adding, updating, or deleting secrets as needed.
        """
        if self.primary:
            vault = VaultManager()
            vault_blueprints_path = os.environ.get("VAULT_BLUEPRINTS_PATH")
            if not vault_blueprints_path:
                raise ValueError("VAULT_BLUEPRINTS_PATH env is not set.")
            if not self.selector:
                raise ValueError("selector is required for primary blueprint.")
            if delete:
                all_blueprint_secrets = vault.list_secrets(path=vault_blueprints_path)
                if self.selector in all_blueprint_secrets:
                    vault.delete_secret(path=f"{vault_blueprints_path}/{self.selector}")
            else:
                blueprint_data = model_to_dict(self)
                blueprint_data.pop("id")
                if self.vault:
                    blueprint_data["vault"] = str(self.vault)
                vault.create_or_update_secret(
                    path=f"{vault_blueprints_path}/{self.selector}",
                    secret_data=blueprint_data,
                )


class JobStateEnum(models.TextChoices):
    PENDING = "PENDING"
    RECEIVED = "RECEIVED"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    REVOKED = "REVOKED"
    RETRY = "RETRY"
    IGNORED = "IGNORED"
    OOMKILLED = "OOMKILLED"
    TIMEOUT = "TIMEOUT"
    UNKNOWN = "UNKNOWN"

    @classmethod
    def get_state(cls, state: str):
        for i in cls:
            if i.lower() == state.lower():
                return i

    @classmethod
    def get_running_states(cls):
        return cls.PENDING, cls.STARTED, cls.RETRY

    @classmethod
    def get_finished_states(cls):
        return (
            cls.SUCCESS,
            cls.FAILURE,
            cls.REVOKED,
            cls.IGNORED,
            cls.TIMEOUT,
            cls.UNKNOWN,
            cls.OOMKILLED,
        )


class Job(models.Model):
    id = models.BigAutoField(primary_key=True, editable=False)
    org_id = models.CharField(max_length=256, null=True, blank=True)
    team_id = models.PositiveIntegerField(null=True, blank=True)
    headers = models.JSONField(default=dict)
    uuid = models.CharField(max_length=256, null=True, blank=True)  # kub job id
    task_id = models.CharField(max_length=255, null=True, blank=True)  # celery task id
    blueprint = models.ForeignKey(JobBlueprint, on_delete=models.SET_NULL, null=True)
    callback_url = models.URLField(null=True, blank=True)
    state = models.CharField(
        max_length=128,
        choices=JobStateEnum.choices,
        default=JobStateEnum.RECEIVED,
        db_index=True,
    )
    input_args = models.JSONField(null=True, blank=True, default=dict)
    args_type = models.CharField(choices=ArgumentsTypeEnum.choices, max_length=128)
    date_created = models.DateTimeField(auto_now_add=True, null=False)
    date_pending = models.DateTimeField(null=True, blank=True)
    date_started = models.DateTimeField(null=True, blank=True)
    date_done = models.DateTimeField(null=True, blank=True)
    logs = models.TextField(null=True, blank=True)
    output = models.BinaryField(null=True, blank=True)
    output_type = models.CharField(
        choices=OutputTypeEnum.choices, max_length=128, null=True, blank=True
    )
    progress = models.IntegerField(
        null=True, blank=True, validators=[MaxValueValidator(100), MinValueValidator(1)]
    )
    reason = models.CharField(max_length=256, null=True, blank=True)
    secret_path = models.CharField(max_length=256, null=True, blank=True)
    revoked_by = models.CharField(max_length=256, null=True, blank=True)
    cpu = models.CharField(max_length=128, null=True, blank=True)
    memory = models.CharField(max_length=128, null=True, blank=True)

    def __repr__(self):
        return f"{self.id}"

    def __str__(self):
        if self.blueprint:
            return self.blueprint.name
        else:
            return "Deleted"

    class Meta:
        """Table information."""

        ordering = ["-date_done"]

    def delete(self, *args, **kwargs):
        self.revoke()
        super().delete(*args, **kwargs)

    def revoke(self):
        from stockhausen.celery import app
        from apps.executor.tasks import terminate_k8s_job

        if self.state not in JobStateEnum.get_finished_states():
            if self.task_id:
                app.control.revoke([self.task_id])
            if self.uuid:
                terminate_k8s_job.delay(self.uuid, self.blueprint_id)

    def get_progress(self):
        return format_html(
            """
            <progress value="{0}" max="100"></progress>
            <span style="font-weight:bold">{0}%</span>
            """,
            self.progress if self.progress else 0,
        )
