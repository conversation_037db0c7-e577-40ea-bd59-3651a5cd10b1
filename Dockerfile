FROM python:3.11-slim

ARG USERNAME=appuser
ARG USER_UID=1000
ARG USER_GID=$USER_UID

ENV PROJECT_ROOT /app
ENV PATH=/home/<USER>/.local/bin:$PATH

WORKDIR $PROJECT_ROOT
RUN mkdir $PROJECT_ROOT/staticfiles
RUN mkdir $PROJECT_ROOT/static

RUN chmod -R 777 $PROJECT_ROOT/staticfiles

RUN adduser --system --group $USERNAME
RUN chown -R $USERNAME:$USERNAME $PROJECT_ROOT

RUN apt-get update \
    && apt-get install -y sudo curl \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 76 /etc/sudoers.d/$USERNAM


RUN chown -R $USERNAME /var/log/
#USER ${USERNAME}

ENV PATH=/app/.local/bin:$PATH \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

RUN pip3 install --upgrade pip setuptools wheel

COPY requirements.txt requirements.txt
RUN python3 -m pip install --upgrade pip
RUN pip3 install -r requirements.txt
RUN mkdir /var/log/gunicorn/
RUN mkdir /var/log/stockhausen/
RUN touch /var/log/gunicorn/access.log
RUN touch /var/log/gunicorn/error.log

EXPOSE 8080

COPY src/ /app
COPY entrypoints/ /entrypoints