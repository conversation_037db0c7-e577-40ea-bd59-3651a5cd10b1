# Generated by Django 4.2 on 2023-04-21 11:29
import django.core.validators
import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="JobBlueprint",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.PositiveIntegerField(
                        choices=[
                            (1, "Internal"),
                            (16, "Docker Hub"),
                            (32, "Ecr"),
                            (64, "Gcr"),
                        ]
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=256, null=True)),
                ("image", models.CharField(max_length=256)),
                ("ttl", models.IntegerField(default=10800)),
                ("max_jobs", models.IntegerField(default=32)),
                (
                    "service_account",
                    models.CharField(blank=True, max_length=256, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Job",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("uuid", models.CharField(blank=True, max_length=256, null=True)),
                ("task_id", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "state",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("RECEIVED", "Received"),
                            ("STARTED", "Started"),
                            ("SUCCESS", "Success"),
                            ("FAILURE", "Failure"),
                            ("REVOKED", "Revoked"),
                            ("RETRY", "Retry"),
                            ("IGNORED", "Ignored"),
                            ("TIMEOUT", "Timeout"),
                        ],
                        default="RECEIVED",
                        max_length=128,
                    ),
                ),
                ("input_args", models.JSONField(blank=True, default=dict, null=True)),
                (
                    "args_type",
                    models.CharField(
                        choices=[("cli", "Cli"), ("env", "Env")], max_length=128
                    ),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_started", models.DateTimeField(blank=True, null=True)),
                ("date_done", models.DateTimeField(auto_now=True)),
                ("logs", models.TextField(blank=True, null=True)),
                ("output", models.BinaryField(blank=True, null=True)),
                (
                    "output_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("application/json", "Json"),
                            ("application/unknown", "Binary"),
                        ],
                        max_length=128,
                        null=True,
                    ),
                ),
                (
                    "progress",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(100),
                            django.core.validators.MinValueValidator(1),
                        ],
                    ),
                ),
                (
                    "blueprint",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="executor.jobblueprint",
                    ),
                ),
            ],
            options={
                "ordering": ["-date_done"],
            },
        ),
    ]
