repos:
  - repo: 'https://github.com/asottile/reorder_python_imports'
    rev: v2.3.0
    hooks:
      - id: reorder-python-imports
        name: 'Reorder Python imports (src, tests)'
        args:
          - '--application-directories'
          - app
  - repo: 'https://github.com/python/black'
    rev: 22.3.0
    hooks:
      - id: black
        name: <PERSON> Formatter (black)
  - repo: 'https://github.com/pycqa/flake8'
    rev: 3.8.2
    hooks:
      - id: flake8
        name: Style Guide Enforcement (flake8)
        args:
          - '--max-line-length=120'
          - --ignore=D100,D203,D405,W503,E203,E501,F841,E126,E712,E123,E131,F821,E121,W605
  - repo: 'https://github.com/asottile/pyupgrade'
    rev: v2.4.3
    hooks:
      - id: pyupgrade
        name: Upgrade syntax for newer versions of the language (pyupgrade)
        args:
          - '--py37-plus'
  - repo: 'https://github.com/pre-commit/pre-commit-hooks'
    rev: v3.1.0
    hooks:
      - id: check-byte-order-marker
      - id: trailing-whitespace
      - id: end-of-file-fixer

#  - repo: 'https://github.com/asottile/dead'
#    rev: v1.3.0
#    hooks:
#      - id: dead
files: src/
