variable "region" {
  description = "default region for AWS Secret Manager"
  type        = string
  #  default     = "us-east-1"
}

variable "username" {
  #  default     = "db_permission"
  type        = string
  description = ""
}

variable "name" {
  description = ""
  type        = string
  #  default     = "sa-db"
}

variable "db_name" {
  description = "DB Name"
  type        = string
}

variable "backup_retention_period" {
  description = "backup retention period of days"
  type        = string
  default     = "7"
}

variable "engine" {
  description = "Database engine for the cluster"
  type        = string
  default     = "aurora-postgresql"
}

variable "writer_instance_class" {
  description = "Writer Instance class for the cluster instances"
  type        = string
  #  default     = "db.t3.medium"
}

variable "reader_instance_class" {
  description = "Reader Instance class for the cluster instances"
  type        = string
  #  default     = "db.t3.medium"
}

variable "env" {
  description = "PROD/DEV"
  type        = string
  #  default     = "PROD"
}

variable "engine_version" {
  description = "Database engine version for the cluster"
  type        = string
  #  default     = "13.5"
}

variable "storage_encrypted" {
  #  description = ""
  type    = bool
  default = true
}

variable "db_port" {
  description = "The db port"
  type        = string
  default     = "5432"
}

variable "db_cluster_parameter_group_family" {
  description = "DB Cluster Parameter group family"
  type        = string
  #  default     = "aurora-postgresql13"
}

variable "db_parameter_group_family" {
  description = "DB Parameter group family"
  type        = string
  #  default     = "aurora-postgresql13"
}


variable "preferred_maintenance_window" {
  description = "The weekly time range during which system maintenance can occur, in (UTC)"
  type        = string
  default     = "wed:10:17-wed:10:47"
}

variable "preferred_backup_window" {
  description = "The daily time range during which automated backups are created if automated backups are enabled using the `backup_retention_period` parameter. Time in UTC"
  type        = string
  default     = "04:00-04:30"
}

variable "deletion_protection" {
  description = "If the DB instance should have deletion protection enabled. The database can't be deleted when this value is set to `true`. The default is `false`"
  type        = bool
  default     = true
}

####
variable "db_ingress_sg_subnets" {
  description = "allow access to the Database from the provided network subnets"
  type        = list(string)
  #    default = [ "*********/16", "**************/32" ]
}

variable "db_tag_description" {
  description = "description field in the tags"
  type        = string
}
####

variable "vpc_tfstate_bucket" {
  type = string
  #    default = "sa-terraform-project"
}

variable "vpc_tfstate_key" {
  type = string
  #    default = "prod/north-virginia/prod-main-vpc/terraform.tfstate"
}

variable "vpc_tfstate_region" {
  type = string
  #    default = "us-west-2"
}

####

