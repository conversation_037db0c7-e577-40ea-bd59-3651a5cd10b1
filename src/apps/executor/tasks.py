import datetime
import logging
import typing
from datetime import timed<PERSON>ta
from inspect import signature

import hvac
from apps.executor.audit_usecases import JobData
from apps.executor.audit_usecases import send_callback as _send_callback
from apps.executor.models import ArgumentsTypeEnum
from apps.executor.models import Job
from apps.executor.models import JobStateEnum
from apps.executor.models import OutputTypeEnum
from apps.executor.task_handlers import ConditionalRevokeHandler
from apps.executor.utils import get_redis_client
from apps.executor.utils import get_redis_revoke_conditions
from apps.executor.utils import Kubernetes
from celery import Task
from celery.exceptions import SoftTimeLimitExceeded
from django.conf import settings
from django.db.models import Case
from django.db.models import CharField
from django.db.models import Count
from django.db.models import DateTimeField
from django.db.models import F
from django.db.models import Q
from django.db.models import Value
from django.db.models import When
from django.utils import timezone
from kubernetes import client
from kubernetes.client.rest import ApiException
from redis.exceptions import RedisError
from stockhausen.celery import app

logger = logging.getLogger("celery")


class BaseTask(Task):
    acks_late = True
    max_retries = 5
    soft_max_retries = 5
    soft_time_limit = 30  # seconds

    @staticmethod
    def extract_arguments(function, *args, **kwargs) -> dict:
        bound_arguments = signature(function).bind(*args, **kwargs)
        bound_arguments.apply_defaults()
        return dict(bound_arguments.arguments)

    def handle_last_retry(self, job_id: int):
        err_msg = f"Marking job [{job_id}] as failed. Last retry reached."
        logger.error(err_msg)
        Job.objects.filter(pk=job_id).update(
            state=JobStateEnum.IGNORED, task_id=self.request.id, reason=err_msg
        )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.error(exc, exc_info=einfo)
        _kwargs = self.extract_arguments(self.run, *args, **kwargs)
        if isinstance(exc, (RedisError, SoftTimeLimitExceeded)):
            if self.request.retries == self.soft_max_retries - 1:
                if "job_id" in kwargs:
                    self.handle_last_retry(_kwargs["job_id"])
            else:
                self.retry(exc=exc, countdown=1, max_retries=self.soft_max_retries)
        else:
            super().on_retry(exc, task_id, args, kwargs, einfo)

    def before_start(self, task_id, args, kwargs):
        super().before_start(task_id, args, kwargs)
        revoke_conditions = get_redis_revoke_conditions(
            settings.CACHE_REVOKE_CONDITIONS_KEY
        )
        if revoke_conditions:
            handler = ConditionalRevokeHandler(revoke_conditions=revoke_conditions)
            handler.handle(headers=self.request.headers, kwargs=kwargs)


class RunK8SJob(BaseTask):
    def before_start(self, task_id, args, kwargs):
        super().before_start(task_id, args, kwargs)
        try:
            self._handle_secret(kwargs["vault_path"], kwargs["vault_version"])
        except Exception:
            logger.error(
                f"Failed to create {kwargs['vault_path']}, {kwargs['vault_version']}"
            )
            raise

    @staticmethod
    def serializer_secret_name(vault_path, vault_version):
        if vault_path and vault_version:
            return f"{vault_path.replace('/', '-')}-{vault_version}"

    @staticmethod
    def _handle_secret(vault_path, vault_version):
        r = get_redis_client()
        secret_path = RunK8SJob.serializer_secret_name(vault_path, vault_version)
        if secret_path:
            cache_exists = r.hget(settings.REDIS_SECRETS, secret_path)
            if cache_exists:
                return secret_path
            k8s = Kubernetes()
            secret_exists = k8s.secret_exists(secret_path)
            if not secret_exists:
                k8s.create_or_replace_secret(
                    name=secret_path,
                    key_value_map=hvac.Client().read(vault_path)["data"]["data"],
                )
            r.hset(settings.REDIS_SECRETS, secret_path, f"{vault_version}")
            return secret_path


@app.task(
    base=RunK8SJob,
    bind=True,
    track_started=True,
    queue=settings.WORKERS_QUEUE,
    time_limit=60,
    acks_late=True,
    max_retries=2000,
)
def run_job(
    self,
    blueprint_id: int,
    job_id: int,
    args_type: str,
    input_args: dict,
    image: str,
    created_at: int = None,
    service_account: str = None,
    vault_path: str = None,
    vault_version: str = None,
    node_toleration: str = None,
    use_persistent_volume: bool = False,
    cpu: str = None,
    memory: str = None,
    max_jobs=16,
):
    secret_provided = bool(vault_path and vault_version)
    prefix = f"run_job id [{job_id}] blueprint_id [{blueprint_id}] headers [{self.request.headers}];secrets {secret_provided}"
    update_data = {}

    r = get_redis_client()
    running_count = r.hget(settings.CACHE_RUNNING_KEY, blueprint_id)
    if running_count and int(running_count) >= max_jobs:
        now = timezone.now()
        if (
            created_at
            and int(now.timestamp() - created_at) > settings.RECEIVED_STATE_TIMEOUT
        ):
            logger.info(f"{prefix};Job {id}; RECEIVED state timeout reached.")
            Job.objects.filter(pk=job_id).update(
                date_pending=now,
                date_started=now,
                date_done=now,
                state=JobStateEnum.IGNORED,
                reason="RECEIVED state timeout reached.",
            )
            return
        logger.info(
            f"{prefix};Job limit {max_jobs} exceeded; retry(countdown=5, priority=4)"
        )
        raise self.retry(countdown=5, priority=4)
    _input_args = None
    env_vars = {
        "REQUEST_ID": str(job_id),
        "EXECUTOR_POST_RESULT_URI": f"{settings.SERVER_HOST}/api/v1/job/{job_id}/patch/",
        "SA_EVENT_URL": settings.SA_EVENT_URL,
    }
    _uuid = f"job-{job_id}"
    try:
        k8s = Kubernetes()
        logger.info(f"{prefix};Creating job [{_uuid}]; image [{image}]")

        if args_type == ArgumentsTypeEnum.ENV:
            env_vars.update(input_args)
        elif args_type == ArgumentsTypeEnum.CLI:
            _input_args = input_args

        volume_mounts, volumes = None, None
        if use_persistent_volume:
            volumes = [
                client.V1Volume(
                    name="persistent-storage",
                    persistent_volume_claim=client.V1PersistentVolumeClaimVolumeSource(
                        claim_name="faas-jobs-efs-claim"
                    ),
                ),
            ]
            volume_mounts = [
                client.V1VolumeMount(name="persistent-storage", mount_path="/tmp")
            ]
        secret_path = RunK8SJob.serializer_secret_name(vault_path, vault_version)
        containers = [
            k8s.create_container(
                image,
                _uuid,
                secret=secret_path,
                pull_policy="IfNotPresent",
                env_vars=env_vars,
                input_args=_input_args,
                volume_mounts=volume_mounts,
                cpu=cpu,
                memory=memory,
            )
        ]

        pod_spec = k8s.create_pod_template(
            _uuid,
            containers,
            toleration=node_toleration,
            volumes=volumes,
            service_account=service_account,
        )
        _job = k8s.create_job(_uuid, pod_spec)
        update_data["uuid"] = _uuid
        update_data["state"] = JobStateEnum.PENDING
        update_data["date_pending"] = timezone.now()
        r.hincrby(settings.CACHE_RUNNING_KEY, blueprint_id, 1)
    except Exception as e:
        trimmed_exception = str(e)[-80:]
        err_msg = f"{prefix};Error when creating;{trimmed_exception}"
        if self.request.retries == self.max_retries:
            logger.error(err_msg)
            update_data["reason"] = err_msg
            update_data["state"] = JobStateEnum.IGNORED
        else:
            logger.error(err_msg)
            update_data["reason"] = err_msg
            update_data["state"] = JobStateEnum.RETRY
            raise self.retry(exc=e, countdown=1)
    finally:
        Job.objects.filter(pk=job_id).update(**update_data)


@app.task(queue=settings.WORKERS_CLEANUP_QUEUE)
def handle_job_callback(job_id, received_time, state, terminate: bool = True, **kwargs):
    fields_to_update = kwargs
    if "date_started" not in kwargs:
        fields_to_update["date_started"] = Case(
            When(date_started__isnull=True, then=Value(received_time)),
            default=F("date_started"),
            output_field=DateTimeField(),
        )
    if state:
        fields_to_update["state"] = Case(
            When(~Q(state__in=JobStateEnum.get_finished_states()), then=Value(state)),
            default=F("state"),
            output_field=CharField(),
        )
    if state in JobStateEnum.get_finished_states():
        if "date_done" not in fields_to_update:
            fields_to_update["date_done"] = timezone.now()
    Job.objects.using("default").filter(id=job_id).update(**fields_to_update)
    job = Job.objects.using("default").get(pk=job_id)
    logger.info(
        f"handle_job_callback: job_id {job_id} state {state} blueprint_id {job.blueprint_id} {fields_to_update}"
    )
    if state in JobStateEnum.get_finished_states():
        if terminate:
            terminate_k8s_job.apply_async(
                kwargs={
                    "job_uuid": job.uuid,
                    "blueprint_id": job.blueprint_id,
                    "grace_period_seconds": 0,
                    "silent": True,
                },
                priority=8,
            )
        else:
            r = get_redis_client()
            r.hincrby(settings.CACHE_RUNNING_KEY, job.blueprint_id, -1)
    if job.callback_url and state in JobStateEnum.get_finished_states():
        logger.info(
            f"handle_job_callback: sending callback {job.id} {job.callback_url}"
        )
        output, output_type = job.output, job.output_type
        if not output or not output_type:
            output_type = OutputTypeEnum.BINARY
        elif isinstance(job.output, memoryview):
            output = bytes(job.output).decode()
        send_callback.delay(
            job.callback_url,
            date_started=job.date_started,
            date_done=job.date_done,
            state=job.state,
            output=output,
            output_type=output_type,
            headers=job.headers,
        )


@app.task(queue=settings.WORKERS_CLEANUP_QUEUE)
def terminate_k8s_job(
    job_uuid, blueprint_id, silent=False, grace_period_seconds: int = 0
):
    logger.info(f"terminate_k8s_job job_uuid[{job_uuid}] blueprint_id[{blueprint_id}]")
    try:
        k8s = Kubernetes()
        k8s.delete_job(job_uuid, grace_period_seconds)
    except ApiException as e:
        if e.status != 404:
            if silent:
                logger.error(e)
            else:
                raise e
    finally:
        r = get_redis_client()
        r.hincrby(settings.CACHE_RUNNING_KEY, blueprint_id, -1)


@app.task(
    queue=settings.WORKERS_CLEANUP_QUEUE,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3},
)
def send_callback(
    url, date_started, date_done, state, output, output_type, headers: dict = None
):
    _send_callback(url, date_started, date_done, state, output, output_type, headers)


@app.task(queue=settings.AUDIT_QUEUE)
def audit_jobs():
    """
    Level 1 auditing checks a Kubernetes job status in case failure of success syncing with the DB.
    """
    prefix = "Audit level 1;"

    k8s = Kubernetes()

    running_kub_jobs = k8s.list_job_jobs(timeout=120)

    if not running_kub_jobs:
        logger.error("No k8s jobs found")
        return

    running_jobs: list[JobData] = (
        Job.objects.filter(
            state=JobStateEnum.STARTED,
            date_created__gt=timezone.now() - timedelta(days=2),
        )
        .annotate(ttl=F("blueprint__ttl"))
        .values(
            "pk",
            "uuid",
            "ttl",
            "state",
            "date_started",
            "callback_url",
            "date_created",
            "blueprint_id",
        )
    )
    uuid_job_mapping: typing.Dict[str, JobData] = {j["uuid"]: j for j in running_jobs}

    for running_job in running_kub_jobs.items:
        job_uuid = running_job.metadata.name
        _prefix = f"{prefix}monitoring job_id[{job_uuid}]"
        saved_job_result: JobData = uuid_job_mapping.pop(job_uuid, None)
        try:
            zombie_job_timeout = timezone.now() - datetime.timedelta(
                settings.STARTED_STATE_TIMEOUT + 500
            )

            if (
                not saved_job_result
                and running_job.metadata.creation_timestamp < zombie_job_timeout
            ):
                logger.debug(f"{_prefix}Detected zombie job {job_uuid};Skipping")
                continue
            if saved_job_result is not None:
                pod = k8s.get_job_pod(job_uuid)
                if not pod or pod.status.phase in ("Pending", "Unknown"):
                    # will be handled in audit_outdated_jobs task
                    continue
                #  checking running job phases
                process_job_phase(
                    log_prefix=_prefix,
                    running_job=running_job,
                    saved_job_result=saved_job_result,
                    pod_phase=pod.status.phase,
                )
        except Exception as e:
            logger.exception(_prefix + str(e))
    not_processed_jobs = []
    if uuid_job_mapping:
        logger.info(f"Detected zombie results {len(uuid_job_mapping)};Investigating")
        now = timezone.now()
        for uuid, job in uuid_job_mapping.items():
            if (
                job["date_started"]
                and job["state"] == JobStateEnum.STARTED
                and job["date_started"] < now - datetime.timedelta(minutes=20)
            ):
                err_msg = f"Found a started task without k8s pod more then 20 min; UUID {uuid}; Marking as Unknown"
                logger.error(err_msg)
                _job = Job.objects.get(id=job["pk"])
                if _job.state == JobStateEnum.STARTED:
                    handle_job_callback.delay(
                        job_id=_job.id,
                        received_time=now,
                        state=JobStateEnum.UNKNOWN,
                        reason=err_msg,
                    )
            else:
                not_processed_jobs.append(uuid)
    if not_processed_jobs:
        logger.info(f"Jobs without self marking {len(not_processed_jobs)}")


@app.task(queue=settings.AUDIT_QUEUE)
def audit_outdated_jobs():
    received_jobs_to_ignore = update_outdated_jobs(
        inital_state=JobStateEnum.RECEIVED,
        source_field="date_created",
        timeout=settings.RECEIVED_STATE_TIMEOUT,
        expected_state=JobStateEnum.IGNORED,
        reason="RECEIVED state timeout reached.",
    )

    pending_jobs_to_ignore = update_outdated_jobs(
        inital_state=[JobStateEnum.PENDING, JobStateEnum.RETRY],
        source_field="date_pending",
        timeout=settings.POD_WAITING_TIMEOUT,
        expected_state=JobStateEnum.IGNORED,
        reason="PENDING/RETRY state timeout reached.",
    )
    not_finished_tasks_to_ignore = update_outdated_jobs(
        inital_state=JobStateEnum.STARTED,
        source_field="date_started",
        timeout=settings.STARTED_STATE_TIMEOUT,
        expected_state=JobStateEnum.TIMEOUT,
        reason="STARTED state timeout reached.",
    )
    cache_sync.delay()
    if (
        received_jobs_to_ignore
        or pending_jobs_to_ignore
        or not_finished_tasks_to_ignore
    ):
        logger.info(
            "audit_outdated_jobs;"
            f"Received to ignore {received_jobs_to_ignore};"
            f"Pending to ignore {pending_jobs_to_ignore}"
            f"Not finished tasks to ignore {not_finished_tasks_to_ignore}"
        )


@app.task(queue=settings.AUDIT_QUEUE)
def cleanup_jobs():
    k8s = Kubernetes()
    now = timezone.now()
    ten_minutes_ago = now - datetime.timedelta(minutes=10)
    label_selector = "status.phase=Succeeded,status.phase=Failed"
    jobs = k8s.list_job_jobs(timeout=120, label_selector=label_selector)
    for job in jobs.items:
        job_completion_time = job.status.completion_time
        if job_completion_time and job_completion_time < ten_minutes_ago:
            logger.info(
                f"Deleting Job {job.metadata.name};time diff [{(job_completion_time - now).total_seconds()}]"
            )
            k8s.delete_job(name=job.metadata.name)
    cache_sync.delay()


@app.task(queue=settings.AUDIT_QUEUE)
def cache_sync():
    blueprint_id_count_pairs = (
        Job.objects.filter(
            state__in=JobStateEnum.get_running_states(),
            blueprint_id__isnull=False,
            date_created__gt=timezone.now() - timedelta(days=2),
        )
        .values("blueprint_id")
        .annotate(count=Count("blueprint_id"))
    )
    data_dict = {
        pair["blueprint_id"]: pair["count"] for pair in blueprint_id_count_pairs
    }
    r = get_redis_client()
    if data_dict:
        with r.pipeline() as pipe:
            pipe.delete(settings.CACHE_RUNNING_KEY)
            pipe.hset(settings.CACHE_RUNNING_KEY, mapping=data_dict)
            pipe.execute()
    else:
        r.delete(settings.CACHE_RUNNING_KEY)


@app.task(queue=settings.AUDIT_QUEUE)
def cleanup_expire_revoke_conditions():
    r = get_redis_client()
    max_score = (
        timezone.now() - datetime.timedelta(hours=settings.CACHE_REVOKE_CONDITIONS_TTL)
    ).timestamp()
    r.zremrangebyscore(settings.CACHE_REVOKE_CONDITIONS_KEY, min=0, max=max_score)


def update_outdated_jobs(
    inital_state: JobStateEnum | list[JobStateEnum],
    source_field: str,
    timeout,
    expected_state=JobStateEnum.IGNORED,
    reason: str = None,
    terminate=True,
):
    filters = {
        f"{source_field}__lt": timezone.now() - datetime.timedelta(seconds=timeout),
        "date_created__gt": timezone.now() - timedelta(days=2),
    }
    if isinstance(inital_state, list):
        filters["state__in"] = inital_state
    else:
        filters["state"] = inital_state
    outdated_jobs = Job.objects.filter(**filters)
    if terminate:
        for job in outdated_jobs:
            if job.uuid:
                terminate_k8s_job.delay(
                    job.uuid, blueprint_id=job.blueprint_id, silent=True
                )
            process_callback(job_id=job.id, callback_url=job.callback_url)
    return outdated_jobs.update(
        state=expected_state, date_done=timezone.now(), reason=reason
    )


def process_job_phase(log_prefix, running_job, saved_job_result: JobData, pod_phase):
    job_uuid = running_job.metadata.name
    match pod_phase:
        case "Running":
            elapsed_time_seconds = 0
            if saved_job_result["date_started"]:
                elapsed_time_seconds = (
                    timezone.now() - saved_job_result["date_started"]
                ).total_seconds()
            if elapsed_time_seconds >= saved_job_result["ttl"]:
                logger.info(f"{log_prefix}Job [{job_uuid}] reached TTL;Terminating")
                Job.objects.filter(pk=saved_job_result["pk"]).update(
                    state=Case(
                        When(
                            state__in=JobStateEnum.get_running_states(),
                            then=Value(JobStateEnum.TIMEOUT),
                        ),
                        default=F("state"),
                        output_field=CharField(),
                    ),
                    date_done=timezone.now(),
                    output_type=OutputTypeEnum.BINARY.value,
                )
                process_callback(
                    job_id=saved_job_result["pk"],
                    callback_url=saved_job_result["callback_url"],
                )
                terminate_k8s_job.delay(
                    saved_job_result["uuid"],
                    saved_job_result["blueprint_id"],
                    silent=True,
                )
            elif saved_job_result["state"] == JobStateEnum.PENDING.value:
                Job.objects.filter(pk=saved_job_result["pk"]).update(
                    state=Case(
                        When(
                            state=JobStateEnum.PENDING.value,
                            then=Value(JobStateEnum.STARTED.value),
                        ),
                        default=F("state"),
                        output_field=CharField(),
                    ),
                    date_started=Case(
                        When(date_started__isnull=True, then=Value(timezone.now())),
                        default=F("date_started"),
                        output_field=DateTimeField(),
                    ),
                )

        case _:
            logger.error(f"{log_prefix}Phase of the job [{job_uuid}] is {pod_phase}")
            return False


def process_callback(job_id, callback_url=None):
    job = Job.objects.using("default").get(id=job_id)
    if callback_url is None:
        callback_url = job.callback_url
    send_callback.delay(
        callback_url,
        date_started=job.date_started if job.date_started else job.date_created,
        date_done=job.date_done if job.date_done else timezone.now(),
        state=job.state,
        output=bytes(job.output).decode()
        if isinstance(job.output, memoryview)
        else job.output,
        output_type=job.output_type,
    )
