from rest_framework import status
from rest_framework.response import Response


def server_error(*_, **__):
    """x§
    500 error handler.
    """
    return Response(
        data={"error": "Server Error (500)"},
        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )


def bad_request(*_, **__):
    """
    400 error handler.
    """
    return Response(
        data={"error": "Bad Request (400)"}, status=status.HTTP_400_BAD_REQUEST
    )
