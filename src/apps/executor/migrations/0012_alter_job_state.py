# Generated by Django 4.2 on 2023-12-07 11:52
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("executor", "0011_jobblueprint_use_persistent_volume"),
    ]

    operations = [
        migrations.AlterField(
            model_name="job",
            name="state",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("RECEIVED", "Received"),
                    ("STARTED", "Started"),
                    ("SUCCESS", "Success"),
                    ("FAILURE", "Failure"),
                    ("REVOKED", "Revoked"),
                    ("RETRY", "Retry"),
                    ("IGNORED", "Ignored"),
                    ("TIMEOUT", "Timeout"),
                ],
                db_index=True,
                default="RECEIVED",
                max_length=128,
            ),
        ),
    ]
