import asyncio
import json
import time

import requests
from aiobotocore.session import get_session
from botocore.config import Config
from django.conf import settings
from rest_framework import status as http_status
from rest_framework.test import APITestCase

S3_FILE_EXPIRATION = 3600  # second
JOB_RUN_TIMEOUT = 300  # second


class StockHausenAPITestCase(APITestCase):
    SERVICE_HOST = settings.SERVER_HOST
    API_URI = f"{SERVICE_HOST}/api/v1"
    S3_BUCKET = "superannotate-python-sdk-test"
    S3_FILE_PATH = "stockhausen/app.py"

    BLUEPRINT_PAYLOAD = {
        "type": 1,  # INTERNAL
        "name": "test",
        "image": "************.dkr.ecr.us-west-2.amazonaws.com/stockhausen:29",
        "ttl": 10800,  # second
        "max_jobs": 32,  # workers
        "is_active": True
        # "service_account": "sa-faas-jobs-sa",  # default image for python code
    }

    _blueprint = None

    @classmethod
    def setUpClass(cls) -> None:
        cls._blueprint: dict = requests.post(
            f"{cls.API_URI}/job_blueprint/", data=cls.BLUEPRINT_PAYLOAD
        ).json()

    @classmethod
    def tearDownClass(cls) -> None:
        requests.delete(f"{cls.API_URI}/job_blueprint/{cls._blueprint['id']}")

    @classmethod
    async def generate_pre_signed_url(cls) -> str:
        session = get_session()
        async with session.create_client(
            "s3", config=Config(signature_version="s3v4")
        ) as s3_client:
            res = await s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": cls.S3_BUCKET, "Key": cls.S3_FILE_PATH},
                ExpiresIn=S3_FILE_EXPIRATION,
            )
            return res

    def get_result_payload(self):
        executor_uri = asyncio.run(self.generate_pre_signed_url())
        return {
            "org_id": None,
            "team_id": None,
            "args_type": "env",
            "blueprint": self._blueprint["id"],
            "input_args": {
                "EVENT_SOURCE": "environment",
                "EVENT_BODY": json.dumps({"arg1": "arg1", "arg2": "arg2"}),
                "EXECUTOR_URI": executor_uri,
            },
            "callback_url": None,
            "vault_path": None,
            "vault_version": None,
        }

    def test_run_retrieve_job(self):
        job_res_payload = self.get_result_payload()

        data = requests.post(f"{self.API_URI}/job/run/", json=job_res_payload).json()
        job_id = data.pop("id")
        data.pop("headers")
        self.assertDictEqual(data, job_res_payload)
        start_time = time.time()
        while True:
            if time.time() - start_time > JOB_RUN_TIMEOUT:
                print(time.time() - start_time, JOB_RUN_TIMEOUT)
                raise AssertionError("Job does not start.")
            response = requests.get(f"{self.API_URI}/job/{job_id}/")
            response.raise_for_status()
            result_data = response.json()
            if result_data["state"] == "SUCCESS":
                break
            time.sleep(1)
        assert result_data["progress"] == 100
        assert result_data["args_type"] == "env"
        assert result_data["output_type"] == "application/json"
        assert json.loads(result_data["output"]) == {"key": "val"}

    def test_post_result(self):
        job_res_payload = self.get_result_payload()
        data = requests.post(f"{self.API_URI}/job/run/", json=job_res_payload).json()
        result_id = data["id"]
        post_data = {
            "output": "string",
            "output_type": "application/unknown",
            "progress": 69,
        }
        res_data = requests.patch(
            f"{self.API_URI}/job/{result_id}/patch/", data=post_data
        )
        assert res_data.status_code == http_status.HTTP_200_OK
        assert (
            res_data.json()["message"] == f"Update job {result_id} request submitted."
        )
