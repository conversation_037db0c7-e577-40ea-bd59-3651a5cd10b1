module "cluster" {
  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "8.2.0"

  name                              = var.name
  database_name                     = var.db_name
  engine                            = var.engine
  engine_version                    = var.engine_version
  create_db_cluster_parameter_group = true
  create_db_subnet_group            = true
  create_security_group             = true
  publicly_accessible               = false
  master_username                   = var.username
  # password is generated and stored in AWS Secrets Manager
  # you can get the password by navigating to the RDS Console
  # open the secret then retrieve the password value from there
#  master_password                   = random_password.rds_password.result
  port                              = var.db_port
  backup_retention_period           = var.backup_retention_period
  preferred_backup_window           = var.preferred_backup_window
  preferred_maintenance_window      = var.preferred_maintenance_window
  deletion_protection               = var.deletion_protection
  vpc_id                            = data.terraform_remote_state.vpc_info.outputs.vpc_id
  availability_zones                = local.azs
  subnets = [
    data.terraform_remote_state.vpc_info.outputs.database_subnet_az[0],
    data.terraform_remote_state.vpc_info.outputs.database_subnet_az[1],
    data.terraform_remote_state.vpc_info.outputs.database_subnet_az[2],
    data.terraform_remote_state.vpc_info.outputs.database_subnet_az[3]
  ]

#  vpc_security_group_ids = [aws_security_group.rds_permission_sg.id]

  storage_encrypted   = var.storage_encrypted
  apply_immediately   = true
  monitoring_interval = 10

  db_cluster_parameter_group_family = var.db_cluster_parameter_group_family
  db_parameter_group_family         = var.db_parameter_group_family
  enabled_cloudwatch_logs_exports   = ["postgresql"]

  instance_class = var.writer_instance_class
  instances = {
    reader-1 = {
      instance_class = var.reader_instance_class
    }
  }

  security_group_rules = {
    ingress = {
      cidr_blocks = var.db_ingress_sg_subnets
    }
  }

  tags = local.tags
}
