STOCKHAUSEN
===============================

Welcome to the Stockhausen Software, which enables to run your docker images in the kubernetes cloud.


Resources
---------------

- /swagger docs


Inside
---------------
- Django 4.2 | gunicorn --config /app/gunicorn-cfg.py stockhausen.wsgi
- Celery Workers | celery -A stockhausen worker -l info --concurrency=8
- Celery Beat | celery -A stockhausen beat

Questions and Issues
--------------------

For questions and issues please use this repo’s issue tracker on GitHub <NAME_EMAIL>.



Payload example for the Zimmer
==============================


POST /api/v1/run_job/
"SOURCE_TYPE": "url",
.. code-block::
    {
      "input_args": {
          "EVENT_SOURCE": "environment",
          "EXECUTOR_URI": "https://superannotate-python-sdk-test.s3.us-east-1.amazonaws.com/stockhausen/app.py?response-content-disposition=inline&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEL%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDGV1LWNlbnRyYWwtMSJIMEYCIQDs0UKJDrDnPK%2FLvgsj%2BHcDBWTzaM9QfMdwpwu0zMEjKgIhAKpZDuUNK0EbvPYIM2oeMCg5EyKCfiMb2knHSDWPsHLbKogDCKj%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQAxoMODI5Mzk4OTc3MDgyIgwc1ffTRqYYChS9CNUq3AJbBmCM7dauixxPq7fefgMFYUXj16rgK46SSimR0f6U7510tuPS2uzcGntgYhrHJb2WCvV848d%2FB5d1EfjSTLWzm6WIVn13VFijVvqxAbAnf4qiwdTQq4dX%2BWcRZMltvW3sGuRrXfR0y9jppowodoAfmkwWs94Eh9XtSII%2FZ2wRSVFnbudRsTBkyvhcqNGMh0yQlAn4m6UIte9rBowwq5FM9GwoJsLBV6GqpDI2lsn9fXArFHNy2W9DpglDJm7M4rHDMTs5viRZW3nbjcgfTkCgLdLpNvlEE9Usp1L%2F50K1FdIromIHYUnGxsuW3U%2F96C6aiD50WzeKkdl9yLOjFbh%2BeCfNV9JwkfidS2cUd1g7flwEwQ1QDNBfjczJyqRo2uVL6TdbGsTtME4hQeNfjmJlT5bf7jR5DRhqvYFDk8hNddhg4Dyy%2FTFBQ%2FDyvcBpDtmXxQv5nB9nV1RBYDMw76DUoQY6sgK74Z8h9F2mwQtBh6FgI0pjUYeVf7eYem2d%2F86Wf9hTWgwup0A9RcAjSp4mP09c0U33TtUR%2FZF0SQoDCVdgozTaDeIlxLXwBD0Gg3iAVWoWnnBCzx7Q%2F8SFp853dn%2FrwRX4YgNOjBdo%2FxfLuhCSRejOp57yygo7cPm9eh4AmEsoyMLYP0Dib2u7gXgNc8BO6k6vKJywmz7PHvtZb31v724n%2B3VmGQHU6FNWmfsAGG8yphpSX7w88ZA0mHbNedXNpXaLjxHCxijutgo8MYlCkaESNJG0TWBfbxfdQ90n%2BvdbaGScKGQPbr5dXTzD0W8GMwxbZWV1ye8%2FcNvfZxQ029%2BrmuiWXQi4pCEQF%2BSiAIF97wIrZS0H%2BoTcRu3NZmgCaVjFKtI%2FnxnOyy6FqUC9ImEfQrQ%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230411T143023Z&X-Amz-SignedHeaders=host&X-Amz-Expires=43200&X-Amz-Credential=ASIA4CHAIBI5DG2IVOT5%2F20230411%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=e17b2027860cc3773802998dbaeadddce5d5707811a02199d47d40b319ff8fdb",
          "EVENT_BODY":  "{\"arg1\": \"arg1\", \"arg2\": \"arg2\"}"
      },
      "args_type": "env",
      "job": 1
    }
