import asyncio
import base64
import datetime
import json
import logging
import re
import uuid
from functools import cache
from functools import lru_cache
from functools import wraps
from typing import Dict
from typing import List

import hvac
import redis
from aiobotocore.session import get_session
from apps.executor.base import SingletonMeta
from apps.executor.confs import RevokeCondition
from apps.executor.confs import RevokePayload
from botocore.config import Config
from django.conf import settings
from django.utils import timezone
from kubernetes import client
from kubernetes import config
from kubernetes.client.rest import ApiException

logging.basicConfig(level=logging.INFO)


def id_generator():
    return f"job-{str(uuid.uuid4())}"


@cache
def get_redis_client() -> redis.StrictRedis:
    return redis.StrictRedis.from_url(settings.REDIS_URL)


class Kubernetes(metaclass=SingletonMeta):
    def __init__(self):
        self._load_k8s_config()
        self.core_api: client.CoreV1Api = client.CoreV1Api()
        self.batch_api = client.BatchV1Api()

    @staticmethod
    def _load_k8s_config():
        try:
            config.load_incluster_config()
        except (
            client.exceptions.OpenApiException,
            config.config_exception.ConfigException,
        ):
            config.load_kube_config()

    def health_check(self):
        self.core_api.get_api_resources()
        self.batch_api.get_api_resources()

    @staticmethod
    def _create_resource_requirements(
        memory: str = None, cpu: str = None
    ) -> client.V1ResourceRequirements | None:
        resources = {}
        if memory is not None:
            resources["memory"] = memory
        if cpu is not None:
            resources["cpu"] = cpu

        if resources:
            return client.V1ResourceRequirements(
                limits=resources,
                requests=resources,
            )
        return None

    @staticmethod
    def create_container(
        image,
        name,
        secret: str = None,
        pull_policy="IfNotPresent",
        volume_mounts: list = None,
        env_vars: dict = None,
        input_args: dict = None,
        memory: str = None,
        cpu: str = None,
        **kwargs,
    ):
        env_list: list[client.V1EnvVar] = [
            client.V1EnvVar(name=env_name, value=env_value)
            for env_name, env_value in (env_vars or {}).items()
        ]
        resource_requirements = Kubernetes._create_resource_requirements(memory, cpu)
        env_from = (
            [client.V1EnvFromSource(secret_ref=client.V1SecretEnvSource(name=secret))]
            if secret
            else None
        )
        container = client.V1Container(
            image=image,
            name=name,
            image_pull_policy=pull_policy,
            env=env_list,
            env_from=env_from,
            volume_mounts=volume_mounts,
            resources=resource_requirements,
            **kwargs,
        )
        if input_args:
            container.args = [json.dumps(input_args)]

        logging.info(
            f"Created container with name: {container.name}, "
            f"image: {container.image} and args: {container.args}"
        )

        return container

    @staticmethod
    def _generate_tolerations_and_selector(
        toleration: str,
    ) -> tuple[list[client.V1Toleration] | None, dict[str, str] | None]:
        if toleration:
            node_selector_key, node_selector_value = "CONSUMER", toleration
            return [
                client.V1Toleration(
                    key=node_selector_key,
                    operator="Equal",
                    value=node_selector_value,
                    effect="NoSchedule",
                )
            ], {node_selector_key: node_selector_value}
        return None, None

    @staticmethod
    def create_pod_template(
        pod_name,
        containers,
        toleration: str = None,
        volumes: list = None,
        service_account=None,
    ):
        tolerations, node_selector = Kubernetes._generate_tolerations_and_selector(
            toleration
        )
        pod_template = client.V1PodTemplateSpec(
            spec=client.V1PodSpec(
                affinity=client.V1NodeAffinity(
                    required_during_scheduling_ignored_during_execution=client.V1NodeSelectorTerm(
                        match_expressions=[
                            client.V1NodeSelectorRequirement(
                                key="gpu-enabled", operator="DoesNotExist"
                            ),
                            client.V1NodeSelectorRequirement(
                                key="gpu-enabled", operator="NotIn", values=["true"]
                            ),
                        ]
                    )
                ),
                restart_policy="Never",
                containers=containers,
                service_account_name=service_account,
                volumes=volumes,
                node_selector=node_selector,
                tolerations=tolerations,
            ),
            metadata=client.V1ObjectMeta(
                name=pod_name,
                labels={"pod_name": pod_name},
                annotations={"cluster-autoscaler.kubernetes.io/safe-to-evict": "false"},
            ),
        )

        return pod_template

    def create_job(self, job_name, pod_template):
        metadata = client.V1ObjectMeta(name=job_name, labels={"job_name": job_name})
        pod_failure_policy = client.V1PodFailurePolicy(
            rules=[
                # If the pod is OOMKilled or other container failures, do not retry
                client.V1PodFailurePolicyRule(
                    action="FailJob",
                    on_exit_codes=client.models.V1PodFailurePolicyOnExitCodesRequirement(
                        operator="In",
                        values=[137, 139],  # 137 = OOMKilled, 139 = SegFault
                    ),
                ),
                # If the pod fails due to a Kubernetes scheduling issue, retry
                client.models.V1PodFailurePolicyRule(
                    action="Ignore",
                    on_pod_conditions=[
                        client.models.V1PodFailurePolicyOnPodConditionsPattern(
                            status="True", type="Unschedulable"
                        )
                    ],
                ),
            ]
        )
        job = client.V1Job(
            api_version="batch/v1",
            kind="Job",
            metadata=metadata,
            spec=client.V1JobSpec(
                backoff_limit=3,
                template=pod_template,
                ttl_seconds_after_finished=0,
                pod_failure_policy=pod_failure_policy,
            ),
        )
        return self.batch_api.create_namespaced_job(settings.KUBERNETES_NAMESPACE, job)

    def list_job_jobs(self, timeout=60, **kwargs):
        return self.batch_api.list_namespaced_job(
            settings.KUBERNETES_NAMESPACE,
            pretty=True,
            timeout_seconds=timeout,
            **kwargs,
        )

    def list_pods(self, label_selector):
        return self.core_api.list_namespaced_pod(
            namespace=settings.KUBERNETES_NAMESPACE,
            watch=False,
            label_selector=label_selector,
        ).items

    def get_job_pod(self, job_name):
        selector = f"job-name={job_name}"
        pods = self.list_pods(label_selector=selector)
        if not pods:
            return None
        else:
            return pods[0]

    def delete_job(self, name: str, grace_period_seconds: int = 0):
        return self.batch_api.delete_namespaced_job(
            name,
            settings.KUBERNETES_NAMESPACE,
            grace_period_seconds=grace_period_seconds,
            propagation_policy="Background",
        )

    def get_pod_logs(self, pod_name: str):
        return self.core_api.read_namespaced_pod_log(
            pod_name, settings.KUBERNETES_NAMESPACE, tail_lines=400
        )

    def delete_empty_pods(self, phase="Succeeded"):
        """
        Pods are never empty, just completed the lifecycle.
        As such they can be deleted.
        Pods can be without any running container in 2 states:
        Succeeded and Failed. This call doesn't terminate Failed pods by default.
        """
        try:
            pods = self.core_api.list_namespaced_pod(
                settings.KUBERNETES_NAMESPACE, pretty=True, timeout_seconds=60
            )
        except ApiException as e:
            raise Exception(
                f"Exception when calling CoreV1Api->list_namespaced_pod: {e}"
            )
        for pod in pods.items:
            logging.debug(pod)
            pod_name = pod.metadata.name
            try:
                if pod.status.phase == phase:
                    # api_response = api_pods.delete_namespaced_pod(pod_name, namespace, delete_options)
                    api_response = self.core_api.delete_namespaced_pod(
                        pod_name, settings.KUBERNETES_NAMESPACE
                    )
                    logging.info(f"Pod: {pod_name} deleted!")
                    logging.debug(api_response)
                else:
                    logging.info(
                        "Pod: {} still not done... Phase: {}".format(
                            pod_name, pod.status.phase
                        )
                    )
            except ApiException as e:
                logging.error(
                    "Exception when calling CoreV1Api->delete_namespaced_pod: %s\n" % e
                )
        return

    @staticmethod
    def _serializer_k8s_secrets(data: dict) -> dict:
        return {
            key: base64.b64encode(value.encode()).decode()
            for key, value in data.items()
        }

    def create_secret(self, name: str, key_value_map: dict):
        metadata = client.V1ObjectMeta(name=name)
        secret = client.V1Secret(
            metadata=metadata, data=self._serializer_k8s_secrets(key_value_map)
        )
        self.core_api.create_namespaced_secret(
            namespace=settings.KUBERNETES_NAMESPACE, body=secret
        )

    def secret_exists(self, name: str):
        try:
            self.core_api.read_namespaced_secret(
                name=name, namespace=settings.KUBERNETES_NAMESPACE
            )
            return True
        except client.exceptions.ApiException:
            return False

    def replace_secret(self, name: str, key_value_map: dict):
        metadata = client.V1ObjectMeta(name=name)
        secret = client.V1Secret(
            metadata=metadata, data=self._serializer_k8s_secrets(key_value_map)
        )
        self.core_api.replace_namespaced_secret(
            namespace=settings.KUBERNETES_NAMESPACE, name=name, body=secret
        )

    def create_or_replace_secret(self, name: str, key_value_map: dict):
        metadata = client.V1ObjectMeta(name=name)
        secret = client.V1Secret(
            metadata=metadata, data=self._serializer_k8s_secrets(key_value_map)
        )
        try:
            self.core_api.create_namespaced_secret(
                namespace=settings.KUBERNETES_NAMESPACE, body=secret
            )
        except client.exceptions.ApiException as e:
            if e.status == 409:
                self.core_api.replace_namespaced_secret(
                    namespace=settings.KUBERNETES_NAMESPACE, name=name, body=secret
                )
            else:
                raise

    def delete_secret(self, name):
        self.core_api.delete_namespaced_secret(
            namespace=settings.KUBERNETES_NAMESPACE, name=name
        )


def timed_lru_cache(ttl=60000):  # miliseonds
    def wrapper_cache(func):
        func = lru_cache(maxsize=32)(func)
        func.lifetime = datetime.timedelta(milliseconds=ttl)
        func.expiration = datetime.datetime.utcnow() + func.lifetime

        @wraps(func)
        def wrapped_func(*args, **kwargs):
            if datetime.datetime.utcnow() >= func.expiration:
                func.cache_clear()
                func.expiration = datetime.datetime.utcnow() + func.lifetime
            return func(*args, **kwargs)

        return wrapped_func

    return wrapper_cache


@timed_lru_cache(ttl=5000)
def get_redis_map(name, key):
    r = get_redis_client()
    return r.hget(name, key)


@timed_lru_cache(ttl=500)
def get_redis_revoke_conditions(key, start=0, end=-1):
    r = get_redis_client()
    revoke_conditions = []
    for revoke_obj in r.zrange(key, start, end):
        revoke_obj = json.loads(revoke_obj)
        conditions = [RevokeCondition(**c) for c in revoke_obj["condition"]]
        revoke_conditions.append(
            RevokePayload(condition=conditions, revoked_by=revoke_obj.get("revoked_by"))
        )
    return revoke_conditions


def set_secret(name: str):
    """
    :return: true if key created else false
    """
    r = get_redis_client()
    v = r.hset(settings.REDIS_SECRET_FLAGS, "name", "")
    return bool(v)


def set_redis_revoke_condition(condition):
    r = get_redis_client()
    r.zadd(
        settings.CACHE_REVOKE_CONDITIONS_KEY,
        {json.dumps(condition): timezone.now().timestamp()},
    )


def extract_numbers(key):
    numbers = re.findall(r"\d+", key)
    return list(map(int, numbers))


async def generate_signed_url(s3_client, key) -> str:
    return await s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": settings.AWS_LOGS_BUCKET_NAME, "Key": key},
    )


async def list_sign_urls(
    bucket: str, prefix: str, size_limit: int | None = None
) -> list[str]:
    session = get_session()
    async with session.create_client(
        "s3", config=Config(signature_version="s3v4")
    ) as s3_client:
        response = await s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
        last_logs = sorted(
            [obj for obj in response.get("Contents", [])],
            key=lambda item: extract_numbers(item["Key"]),
            reverse=True,
        )
        total_size = 0
        log_paths = []
        for item in last_logs:
            item_size = item["Size"]
            item_path = item["Key"]
            if size_limit and total_size + item_size > size_limit and log_paths:
                break
            log_paths.append(item_path)
            total_size += item_size

        if log_paths:
            sign_urls = await asyncio.gather(
                *[generate_signed_url(s3_client, path) for path in log_paths]
            )
            sign_urls.reverse()  # noqa
            return sign_urls  # noqa
    return []


class VaultManager:
    def __init__(self, mount_point: str = "kv"):
        self.client = hvac.Client()
        assert self.client.is_authenticated()
        self.mount_point = mount_point

    def _normalize_path(self, path: str) -> str:
        if path.startswith(f"{self.mount_point}/data/"):
            return path[len(f"{self.mount_point}/data/") :]
        return path

    def list_secrets(self, path: str) -> List[str]:
        """
        List secret keys at a given path.
        """
        path = self._normalize_path(path)
        response = self.client.secrets.kv.v2.list_secrets(
            path=path, mount_point=self.mount_point
        )
        return response["data"]["keys"]

    def read_secret(self, path: str) -> Dict:
        """
        Read the latest version of a secret.
        """
        path = self._normalize_path(path)
        response = self.client.secrets.kv.v2.read_secret_version(
            path=path, mount_point=self.mount_point
        )
        return response["data"]["data"]

    def create_or_update_secret(self, path: str, secret_data: Dict) -> None:
        """
        Create or update a secret at the given path.
        """
        path = self._normalize_path(path)
        self.client.secrets.kv.v2.create_or_update_secret(
            path=path, secret=secret_data, mount_point=self.mount_point
        )

    def delete_secret(self, path: str) -> None:
        """
        Permanently delete all versions and metadata of the secret.
        """
        path = self._normalize_path(path)
        self.client.secrets.kv.v2.delete_metadata_and_all_versions(
            path=path, mount_point=self.mount_point
        )
