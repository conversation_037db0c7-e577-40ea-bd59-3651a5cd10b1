import os
from unittest.mock import patch

from apps.executor.models import JobBlueprint
from django.core.management import call_command
from django.test import TestCase


class SyncBlueprintsCommandTest(TestCase):
    def setUp(self):
        patcher = patch.dict(
            os.environ,
            {
                "VAULT_BLUEPRINTS_PATH": "kv/data/dev/sa-faas/blueprints",
                "VAULT_ADDR": "https://vault.k8s.devsuperannotate.com",
                "VAULT_TOKEN": "hvs.CAESIOSBllsYfKxs9w31e1YZmjSr2nnvj543lsXFd-g6XT9qGh4KHGh2cy5kVmlMMHkxZWlMSUMyaU5MMk9leGR3aUk",
            },
            clear=False,
        )
        self.addCleanup(patcher.stop)
        patcher.start()

    def test_command_run(self):
        call_command("sync_blueprints")
        assert JobBlueprint.objects.all()
