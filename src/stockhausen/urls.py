from apps.executor.views import HealthCheck
from apps.executor.views import JobBlueprintViewSet
from apps.executor.views import JobViewSet
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include
from django.urls import path
from django.urls import re_path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import routers

schema_view = get_schema_view(
    openapi.Info(
        title="Stockhausen API",
        default_version="v1",
        description="Stockhausen",
        contact=openapi.Contact(email="<EMAIL>"),
    ),
    # url="127.0.0.1",
    public=True,
)

router = routers.SimpleRouter()
router.register(r"job_blueprint", JobBlueprintViewSet)
router.register(r"job", JobViewSet)

urlpatterns = [
    re_path(
        r"^swagger(?P<format>\.json|\.yaml)$",
        schema_view.without_ui(cache_timeout=0),
        name="schema-json",
    ),
    re_path(
        r"^swagger/$",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("api/v1/", include(router.urls)),
    path("admin/", admin.site.urls),
    path("healthCheck/", HealthCheck.as_view()),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

handler500 = "rest_framework.exceptions.server_error"
handler400 = "rest_framework.exceptions.bad_request"
