import json
import uuid

from apps.executor.models import Job
from apps.executor.models import JobBlueprint
from apps.executor.tasks import run_job
from apps.executor.utils import set_redis_revoke_condition
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.utils import timezone
from rest_framework import serializers


class StrictStringJSONField(serializers.JSONField):
    def to_internal_value(self, data):
        """Ensure all keys and values are strings."""
        if not isinstance(data, dict):
            raise serializers.ValidationError("Data must be a dictionary.")

        for key, value in data.items():
            if not isinstance(key, str):
                raise serializers.ValidationError(
                    f"Invalid key type: {key} (must be a string)."
                )
            if not isinstance(value, str):
                raise serializers.ValidationError(
                    f"Invalid value type for key '{key}': {value} (must be a string)."
                )

        return data


class CachedPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    def to_internal_value(self, data):
        if self.pk_field is not None:
            data = self.pk_field.to_internal_value(data)
        queryset = self.get_queryset()
        try:
            if isinstance(data, bool):
                raise TypeError
            return queryset.model(pk=data)
        except ObjectDoesNotExist:
            self.fail("does_not_exist", pk_value=data)
        except (TypeError, ValueError):
            self.fail("incorrect_type", data_type=type(data).__name__)


class BinaryField(serializers.Field):
    def to_representation(self, value):
        if isinstance(value, str):
            return value
        else:
            try:
                return bytes(value).decode("utf-8")
            except Exception:
                return None

    def to_internal_value(self, value):
        if isinstance(value, (dict, list)):
            return str.encode(json.dumps(value))
        return str.encode(value)


class JobBlueprintSerializer(serializers.ModelSerializer):
    class Meta:
        model = JobBlueprint
        fields = "__all__"


class AnyField(serializers.Field):
    def to_representation(self, value):
        return value

    def to_internal_value(self, data):
        return data


class RevokeConditionSerializer(serializers.Serializer):
    op_choices = [("==", "=="), (">=", ">="), ("<=", "<="), (">", ">"), ("<", "<")]

    source = serializers.CharField(max_length=256)
    key = serializers.CharField(max_length=256)
    op = serializers.ChoiceField(choices=op_choices)
    value = AnyField(required=True)

    @staticmethod
    def validate_value(value):
        if type(value) not in (str, int, float, bool):
            raise serializers.ValidationError("Invalid type")
        return value


class JobRevokeConditionSerializer(serializers.Serializer):
    condition = RevokeConditionSerializer(required=True, many=True)
    revoked_by = serializers.CharField(max_length=256, required=False, allow_blank=True)

    def to_internal_value(self, data):
        revoked_by = data.get("revoked_by")
        if not revoked_by:
            request = self.context.get("request")
            user_agent = request.META.get("HTTP_USER_AGENT")
            if user_agent:
                data["revoked_by"] = user_agent[:256]
        return super().to_internal_value(data)

    def save(self, **kwargs):
        set_redis_revoke_condition(self.validated_data)


class JobReadSerializer(serializers.ModelSerializer):
    output = BinaryField(required=False)

    class Meta:
        model = Job
        fields = "__all__"
        read_only_fields = (
            "uuid",
            "pod_id",
            "logs",
            "state",
            "date_created",
            "date_done",
            "output",
            "output_type",
            "date_started",
        )


class JobCreateSerializer(serializers.ModelSerializer):
    selector = serializers.CharField(max_length=256, required=False, allow_blank=True)
    blueprint = serializers.PrimaryKeyRelatedField(
        queryset=JobBlueprint.objects.all(), required=False, allow_null=True
    )
    priority = serializers.IntegerField(
        max_value=10, min_value=1, required=False, allow_null=True, write_only=True
    )
    input_args = StrictStringJSONField(required=True)
    headers = serializers.JSONField(required=False)
    vault_path = serializers.CharField(max_length=512, allow_null=True, required=False)
    vault_version = serializers.IntegerField(allow_null=True, required=False)

    def validate(self, attrs):
        selector, blueprint = attrs.get("selector"), attrs.get("blueprint")
        if not (selector or blueprint):
            raise serializers.ValidationError(
                "one of [blueprint, selector] is required"
            )
        if selector and not JobBlueprint.objects.filter(selector=selector).exists():
            raise serializers.ValidationError(
                {"selector": f"Blueprint [{selector}] does not exist"}
            )
        return super().validate(attrs)

    class Meta:
        model = Job
        fields = (
            "id",
            "org_id",
            "priority",
            "team_id",
            "input_args",
            "args_type",
            "blueprint",
            "callback_url",
            "selector",
            "headers",
            "vault_path",
            "vault_version",
        )

    @staticmethod
    def _validate_blueprint(blueprint):
        if not blueprint.is_active:
            raise serializers.ValidationError("The action has been canceled.")

    @staticmethod
    def _update_headers(headers, instance):
        headers["blueprint_id"] = headers.get("blueprint_id", instance.blueprint_id)
        headers["x_received_timestamp"] = headers.get(
            "x_received_timestamp", timezone.now().timestamp()
        )
        return headers

    @staticmethod
    def _schedule_job(
        instance, blueprint, priority, headers, vault_path, vault_version
    ):
        run_job.apply_async(
            task_id=instance.task_id,
            kwargs={
                "blueprint_id": blueprint.id,
                "job_id": instance.id,
                "created_at": instance.date_created.timestamp(),
                "args_type": instance.args_type,
                "input_args": instance.input_args,
                "image": instance.blueprint.image,
                "service_account": instance.blueprint.service_account,
                "node_toleration": instance.blueprint.node_toleration,
                "use_persistent_volume": instance.blueprint.use_persistent_volume,
                "max_jobs": instance.blueprint.max_jobs,
                "cpu": instance.blueprint.cpu,
                "memory": instance.blueprint.memory,
                "vault_path": vault_path,
                "vault_version": vault_version,
            },
            priority=priority,
            headers=headers,
        )

    @staticmethod
    def _handle_secrets(blueprint, vault_path, vault_version):
        if vault_path:
            return vault_path, vault_version
        if blueprint.vault:
            return blueprint.vault.path, blueprint.vault.version
        return None, None

    def create(self, validated_data):
        selector = validated_data.pop("selector", None)
        blueprint = validated_data.pop("blueprint", None)
        vault_path = validated_data.pop("vault_path", None)
        vault_version = validated_data.pop("vault_version", None)

        if not blueprint:
            blueprint = JobBlueprint.objects.get(selector=selector)
        self._validate_blueprint(blueprint)
        vault_path, vault_version = self._handle_secrets(
            blueprint, vault_path, vault_version
        )
        headers = validated_data.get("headers", {})
        validated_data["blueprint"] = blueprint
        validated_data["task_id"] = str(uuid.uuid4())
        validated_data["cpu"] = blueprint.cpu
        validated_data["memory"] = blueprint.memory
        validated_data["secret_path"] = f"{vault_path}:{vault_version}"

        with transaction.atomic():
            priority = validated_data.pop("priority", blueprint.priority)
            instance = super().create(validated_data)
            headers = self._update_headers(headers, instance)
            self._schedule_job(
                instance=instance,
                blueprint=blueprint,
                priority=priority,
                headers=headers,
                vault_path=vault_path,
                vault_version=vault_version,
            )
        return instance


class PostResultSerializer(serializers.ModelSerializer):
    output = BinaryField(required=False)

    class Meta:
        model = Job
        fields = (
            "output",
            "output_type",
            "progress",
            "state",
            "date_started",
            "date_done",
        )
