import os

from celery import Celery
from celery.signals import setup_logging
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "stockhausen.settings")
app = Celery("proj")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()
app.conf.task_ignore_result = False
app.conf.task_store_errors_even_if_ignored = True
app.conf.task_track_started = True
app.conf.worker_detect_quorum_queues = True
app.conf.broker_transport_options = {"confirm_publish": True}
app.conf.broker_connection_retry = True
app.conf.broker_connection_retry_on_startup = True
app.conf.task_reject_on_worker_lost = True

app.conf.beat_schedule = {
    "audit_jobs": {
        "task": "apps.executor.tasks.audit_jobs",
        "schedule": 60,
        "options": {"queue": settings.AUDIT_QUEUE},
    },
    "audit_outdated_jobs": {
        "task": "apps.executor.tasks.audit_outdated_jobs",
        "schedule": 60,
        "options": {"queue": settings.AUDIT_QUEUE},
    },
    "cache_sync": {
        "task": "apps.executor.tasks.cache_sync",
        "schedule": 3600,
        "options": {"queue": settings.AUDIT_QUEUE},
    },
    "cleanup_jobs": {
        "task": "apps.executor.tasks.cleanup_jobs",
        "schedule": 60,
        "options": {"queue": settings.AUDIT_QUEUE},
    },
    "cleanup_expire_revoke_conditions": {
        "task": "apps.executor.tasks.cleanup_expire_revoke_conditions",
        "schedule": 10800,
        "options": {"queue": settings.AUDIT_QUEUE},
    },
}


@setup_logging.connect
def config_loggers(*args, **kwargs):
    from logging.config import dictConfig
    from django.conf import settings

    dictConfig(settings.LOGGING)
