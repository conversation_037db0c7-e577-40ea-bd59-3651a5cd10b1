import boto3
from admin_list_charts.admin import ListChartMixin
from apps.executor.models import Job
from apps.executor.models import JobBlueprint
from apps.executor.models import VaultSecret
from apps.executor.utils import extract_numbers
from apps.executor.utils import get_redis_client
from botocore.config import Config
from django import forms
from django.conf import settings
from django.contrib import admin
from django.contrib import messages
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django_admin_inline_paginator.admin import TabularInlinePaginated


def revoke_jobs(modeladmin, request, queryset):  # noqa
    blueprint_ids = queryset.values_list("id", flat=True)
    r = get_redis_client()
    for blueprint_id in blueprint_ids:
        r.hset(settings.CACHE_REVOKED_KEY, blueprint_id, timezone.now().timestamp())
    messages.success(request, "Revoked request received.")


revoke_jobs.short_description = "Revoked queued jobs"


class JobBlueprintForm(forms.ModelForm):
    class Meta:
        model = JobBlueprint
        fields = "__all__"
        widgets = {
            "image": forms.TextInput(attrs={"size": 120}),
        }

    def clean(self):
        cleaned_data = super().clean()
        primary = cleaned_data.get("primary")
        selector = cleaned_data.get("selector")

        if primary and not selector:
            self.add_error("selector", "required for primary blueprint.")


class JobResultInline(TabularInlinePaginated):
    model = Job
    ordering = ["-id"]
    exclude = "logs", "input_args", "progress"
    readonly_fields = "get_progress", "link_to_inline_model"
    can_delete = False
    per_page = 10

    @staticmethod
    def link_to_inline_model(obj):
        url = reverse("admin:executor_job_change", args=[obj.id])
        return format_html('<a href="{}">{}</a>', url, obj.id)

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(JobBlueprint)
class JobBlueprintAdmin(admin.ModelAdmin):
    actions = [revoke_jobs]
    form = JobBlueprintForm
    inlines = (JobResultInline,)
    list_filter = ("primary",)
    search_fields = "id", "name", "image", "selector"
    list_display = ("__str__", "type", "image", "service_account", "results_count")

    @staticmethod
    def results_count(obj):
        return Job.objects.filter(blueprint=obj).count()


@admin.register(VaultSecret)
class VaultSecretAdmin(admin.ModelAdmin):
    fields = "path", "version"
    list_display = "path", "version"


@admin.register(Job)
class JobResultAdmin(ListChartMixin, admin.ModelAdmin):
    date_hierarchy = "date_created"
    ordering = ["-id"]
    list_display = (
        "__str__",
        "uuid",
        # "org_id",
        "team_id",
        "date_created",
        "date_started",
        "date_done",
        "get_duration",
        "state",
        "get_progress",
    )
    list_filter = ("state", "blueprint__primary")
    search_fields = "blueprint__name", "uuid", "blueprint__image", "team_id", "org_id"
    readonly_fields = (
        "input_args",
        "get_progress",
        "output_data",
        "last_log_sign_url",
    )

    def has_change_permission(self, request, obj=None):
        return False

    def get_duration(self, obj):
        if obj.date_started and obj.date_done:
            return (obj.date_done - obj.date_started).seconds
        return "N/A"

    @staticmethod
    def output_data(obj):
        try:
            return obj.output.tobytes().decode()
        except Exception:  # noqa
            return obj.output.tobytes()

    @staticmethod
    def last_log_sign_url(obj):
        prefix = f"{settings.AWS_LOGS_PATH}/{obj.uuid}/"
        s3_client = boto3.client("s3", config=Config(signature_version="s3v4"))
        response = s3_client.list_objects_v2(
            Bucket=settings.AWS_LOGS_BUCKET_NAME, Prefix=prefix
        )
        last_logs = sorted(
            [obj for obj in response.get("Contents", [])],
            key=lambda item: extract_numbers(item["Key"]),
            reverse=True,
        )
        if last_logs:
            last_log_key = last_logs[0]["Key"]
            sign_url = s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params={"Bucket": settings.AWS_LOGS_BUCKET_NAME, "Key": last_log_key},
            )
            return format_html(f"<a href='{sign_url}'> Download </a>")
        else:
            return "No logs"

    get_duration.short_description = "Duration (sec)"


admin.site.site_header = "Stockhausen"
