from unittest.mock import patch

from apps.executor.tasks import run_job
from apps.executor.tasks import RunK8<PERSON>Job
from django.test import TestCase
from django.utils import timezone


class TestRunJobRetry(TestCase):
    @patch("apps.executor.tasks.Kubernetes")
    @patch("apps.executor.tasks.hvac")
    @patch("apps.executor.tasks.get_redis_client")
    def test_base_job_before_start(self, get_redis_client, hvac_client, k8s):
        job = RunK8SJob()
        hvac_response = {"data": {"data": {"1": 1, "2": 2}}}
        get_redis_client.return_value.hget.return_value = False
        k8s.return_value.secret_exists.return_value = False
        hvac_client.Client.return_value.read.return_value = hvac_response
        payload = {
            # "self": MagicMock(),
            "blueprint_id": 1,
            "job_id": 1,
            "args_type": "env",
            "input_args": {"key": "val"},
            "image": "image",
            "vault_path": "path/to/vault",
            "vault_version": "version",
        }
        job.before_start(task_id="1", args=[], kwargs=payload)
        assert get_redis_client.return_value.hget.call_args_list[0][0] == (
            "secrets",
            "path-to-vault-version",
        )
        assert k8s.return_value.secret_exists.call_args[0][0] == "path-to-vault-version"
        assert hvac_client.Client().read.return_value == hvac_response
        assert True

    @patch("apps.executor.tasks.Kubernetes")
    @patch("apps.executor.tasks.hvac")
    def test_run_job(self, hvac_client, k8s):
        payload = {
            # "self": MagicMock(),
            "blueprint_id": 1,
            "job_id": 1,
            "created_at": timezone.now().timestamp(),
            "args_type": "env",
            "input_args": {"key": "val"},
            "image": "image",
            "vault_path": "path/to/vault",
            "vault_version": "version",
        }

        run_job(**payload)
