import os
from unittest.mock import MagicMock
from unittest.mock import patch

from apps.executor.models import JobBlueprint
from apps.executor.models import VaultSecret
from django.core.management import call_command
from django.test import TestCase


class SyncBlueprintsCommandTests(TestCase):
    @patch("apps.executor.utils.VaultManager")
    @patch("hvac.Client")
    def test_command_synchronizes_primary_blueprints(
        self, mock_hvac_client_cls, mock_vault_mgr_cls
    ):
        os.environ["VAULT_BLUEPRINTS_PATH"] = "test/path"

        mock_client = MagicMock()
        mock_client.is_authenticated.return_value = True
        mock_hvac_client_cls.return_value = mock_client

        mock_vault_mgr = MagicMock()
        mock_vault_mgr.list_secrets.return_value = ["bp1"]
        mock_vault_mgr.read_secret.return_value = {
            "name": "BP1",
            "selector": "sel1",
            "image": "img1",
            "primary": True,
            "vault": "secret/path:2",
        }
        mock_vault_mgr_cls.return_value = mock_vault_mgr

        with patch.object(
            VaultSecret.objects, "get", side_effect=VaultSecret.DoesNotExist
        ), patch.object(
            VaultSecret.objects,
            "create",
            return_value=VaultSecret(id=1, path="secret/path", version=2),
        ), patch.object(
            JobBlueprint.objects, "update_or_create"
        ) as mock_update_or_create:
            call_command("sync_blueprints")
            mock_update_or_create.assert_called_once_with(
                selector="sel1",
                defaults={
                    "name": "BP1",
                    "selector": "sel1",
                    "image": "img1",
                    "primary": True,
                    "vault_id": 1,
                },
            )
