import filecmp
import json
import logging
import os
import pathlib
import tempfile
from unittest import TestCase

from superannotate import AppException
from superannotate import SAClient
from tests.jobs.sa_export import DATA_SET_PATH

sa = SAClient()


class TestGetAnnotations(TestCase):
    PROJECT_NAME = "Test-sa_export"
    PROJECT_DESCRIPTION = "Desc"
    PROJECT_TYPE = "Vector"
    FOLDER_NAME = "folder"
    EXPORT_ID = 50849128
    TEST_FOLDER_PATH = os.path.join(DATA_SET_PATH, "sample_project_vector")
    CLASSES_PATH = os.path.join(TEST_FOLDER_PATH, "classes/classes.json")
    TEST_FOLDER_PATH_EXPECTED = os.path.join(
        DATA_SET_PATH, "sample_project_vector_expected"
    )
    TEST_FOLDER_PATH_OBJECT_DETECTION_EXPECTED = os.path.join(
        DATA_SET_PATH, "sample_project_vector_object_detection_expected"
    )
    TEST_S3_BUCKET = "superannotate-python-sdk-test"
    TMP_DIR = "TMP_DIR"
    IMAGE_NAME = "example_image_1.jpg"

    @classmethod
    def setUpClass(cls) -> None:
        cls.tearDownClass()
        cls._project = sa.create_project(
            cls.PROJECT_NAME, cls.PROJECT_DESCRIPTION, cls.PROJECT_TYPE
        )  # noqa
        cls.folder = sa.create_folder(
            project=cls.PROJECT_NAME, folder_name=cls.FOLDER_NAME
        )
        sa.create_annotation_classes_from_classes_json(
            cls.PROJECT_NAME, cls.CLASSES_PATH
        )
        sa.upload_images_from_folder_to_project(
            cls.PROJECT_NAME, cls.TEST_FOLDER_PATH, recursive_subfolders=True
        )
        sa.upload_images_from_folder_to_project(
            f"{cls.PROJECT_NAME}/{cls.FOLDER_NAME}", cls.TEST_FOLDER_PATH
        )
        sa.upload_annotations_from_folder_to_project(
            cls.PROJECT_NAME, cls.TEST_FOLDER_PATH, recursive_subfolders=True
        )

    @classmethod
    def tearDownClass(cls) -> None:
        projects = sa.search_projects(cls.PROJECT_NAME, return_metadata=True)
        for project in projects:
            try:
                sa.delete_project(project)
            except AppException as e:
                logging.error(e)

    def test_export(self):
        # self.setup_project()
        export = sa.prepare_export(self.PROJECT_NAME, include_fuse=True)
        with tempfile.TemporaryDirectory() as tmp_dir:
            sa.download_export(
                self.PROJECT_NAME, export, tmp_dir, extract_zip_contents=True
            )
            expected_path = os.path.join(DATA_SET_PATH, self.TEST_FOLDER_PATH_EXPECTED)
            dircmp = filecmp.dircmp(
                os.path.join(DATA_SET_PATH, self.TEST_FOLDER_PATH_EXPECTED), tmp_dir
            )
            assert not any([dircmp.left_only, dircmp.right_only])
            for _json_path in pathlib.Path(tmp_dir).glob("*.json"):
                assert json.load(open(_json_path)) == json.load(
                    open(os.path.join(expected_path, _json_path))
                )
