import unittest
from unittest.mock import MagicMock

from apps.executor.exceptions import TaskRevokedException
from apps.executor.models import JobStateEnum
from apps.executor.task_handlers import ConditionalRevokeHandler
from apps.executor.utils import RevokeCondition
from apps.executor.utils import RevokePayload
from django.test import TestCase


class TestConditionalRevokeHandler(TestCase):
    def setUp(self):
        self.handler = ConditionalRevokeHandler(
            [
                RevokePayload(
                    condition=[
                        RevokeCondition(
                            source="header", key="x", op="==", value="value1"
                        ),
                        RevokeCondition(source="kwargs", key="y.z", op=">=", value=10),
                    ],
                    revoked_by="user1",
                ),
                RevokePayload(
                    condition=[
                        RevokeCondition(
                            source="header", key="x", op=">=", value="value2"
                        ),
                        RevokeCondition(source="kwargs", key="y.z", op="==", value=20),
                    ],
                    revoked_by="user2",
                ),
            ]
        )

    def test_handle_with_matching_condition(self):
        headers = {"x": "value1"}
        kwargs = {"y": {"z": 15}, "job_id": 123}
        job_mock = MagicMock()
        job_filter_mock = MagicMock(return_value=job_mock)
        with unittest.mock.patch(
            "apps.executor.models.Job.objects.filter", job_filter_mock
        ):
            with self.assertRaises(TaskRevokedException):
                self.handler.handle(headers, kwargs)
        job_filter_mock.assert_called_once_with(pk=123)
        job_mock.update.assert_called_once_with(
            state=JobStateEnum.REVOKED,
            revoked_by="user1",
            date_started=unittest.mock.ANY,
            date_done=unittest.mock.ANY,
        )

    def test_handle_with_no_matching_condition(self):
        headers = {"x": "value1"}
        kwargs = {"y": {"z": 5}, "job_id": 123}
        job_filter_mock = MagicMock()
        with unittest.mock.patch(
            "apps.executor.models.Job.objects.filter", job_filter_mock
        ):
            self.handler.handle(headers, kwargs)
        job_filter_mock.assert_not_called()
